# Technical Spike: Bulk Keyword Addition and Favoriting Functionality

## Executive Summary

This technical spike investigates the implementation of bulk keyword addition and favoriting functionality from the business-center-client to the ML business app's keyword tracking system. The analysis covers backend API capabilities, product edition detection, automatic favoriting mechanisms, and keyword limit management.

## 1. Backend API Analysis

### 1.1 Current `updateKeywords` API

**Location**: `libs/local-seo/src/lib/keyword-tracking-page/keyword-tracking/keyword-tracking.service.ts` (lines 622-674)

**API Signature**:
```typescript
updateKeywords(oldKeywords: string[], newKeywords: string[]): void
```

**Key Findings**:
- ✅ **Supports bulk operations**: The API accepts arrays of keywords and handles bulk addition
- ✅ **Automatic favoriting**: Automatically adds new keywords to favorites based on partner settings
- ✅ **Automatic syncing**: Automatically adds new keywords to syncing list based on partner settings
- ✅ **Deduplication**: Uses `Array.from(new Set(newKeywords.concat(oldKeywords)).values())` to prevent duplicates

**API Flow**:
1. Combines old and new keywords with deduplication
2. Updates listing profile with new keyword list via `listingProfileService.update()`
3. Retrieves partner settings to determine auto-favorite/sync behavior
4. Updates local keyword store with new keywords, favorites, and syncing lists

**Partner Settings Integration**:
```typescript
if (!partnerSettings?.isFavoriteNewKeywordsDisabled) {
  favoriteKeywords = Array.from(new Set(newKeywords.concat(favoriteKeywords)).values());
}
if (!partnerSettings?.isSyncKeywordsDisabled) {
  syncingKeywords = Array.from(new Set(newKeywords.concat(syncingKeywords)).values());
}
```

### 1.2 Related APIs

**`updateFavoriteKeywords(keywords: string[])`** (lines 588-620):
- Handles bulk favorite keyword updates
- Updates SEO settings via `SaveSEOSettingsRequest`

**`updateSyncingKeywords(syncingKeywords: string[])`** (lines 676-707):
- Handles bulk syncing keyword updates
- Updates listing profile via `UpdateListingProfileRequest`

## 2. Product Edition Detection

### 2.1 Edition Detection Implementation

**Location**: `libs/local-seo/src/lib/keyword-tracking-page/keyword-tracking/keyword-tracking.service.ts` (lines 154-171)

**Implementation**:
```typescript
this.isLBFreeEdition$ = this.agid$.pipe(
  switchMap((agid) => {
    const filters: ListAppAndAddonActivationStatusFilter = {
      appIds: ['MS'],
      statuses: [AppAndAddonActivationStatus.ACTIVATED, AppAndAddonActivationStatus.CANCELED],
    };
    return accountsService.listAppsAndAddonsActivationStatusesForBusiness(agid, filters).pipe(
      map((activations) => {
        if (activations?.length > 0) {
          switch (activations[0].editionId) {
            case DEMO_PAID_EDITION: // 'EDITION-MXWLTQPN'
              return false;
            case PROD_PAID_EDITION: // 'EDITION-CFH5CKHC'
              return false;
          }
        }
        return true; // Default to free edition
      }),
      shareReplay(1),
    );
  }),
);
```

### 2.2 Edition Constants

**Location**: `libs/local-seo/src/lib/local-seo.ts` (lines 35-36)

```typescript
export const DEMO_PAID_EDITION = 'EDITION-MXWLTQPN';
export const PROD_PAID_EDITION = 'EDITION-CFH5CKHC';
```

### 2.3 Data Source

**API**: `AccountsService.listAppsAndAddonsActivationStatusesForBusiness()`
- **Service**: `@vendasta/accounts/legacy`
- **Returns**: Array of app/addon activation statuses
- **Filter**: Application ID 'MS' (Marketing Suite/Local SEO)

## 3. Keyword Limit Management

### 3.1 Keyword Limit Calculation

**Location**: `libs/local-seo/src/lib/keyword-tracking-page/keyword-tracking/keyword-tracking.service.ts` (lines 261-286)

**Constants**:
```typescript
const ADDON_INCREMENT = 15;
const free_edition_keywords = 3;
const paid_edition_keywords = 15;
```

**Calculation Logic**:
```typescript
this.keywordLimit$ = combineLatest([this.isLBFreeEdition$, this.addOns$]).pipe(
  map(([isFreeEdition, addOns]) => {
    let keywordLimit = free_edition_keywords; // 3

    if (!isFreeEdition) {
      keywordLimit = paid_edition_keywords; // 15
    }

    if (addOns?.length > 0) {
      const limitIncrement = addOns[0].count * ADDON_INCREMENT; // +15 per addon
      keywordLimit = keywordLimit + limitIncrement;
    }

    return keywordLimit;
  }),
);
```

### 3.2 Add-on Detection

**API**: `SEOApiService.getActiveSeoAddons()`
- **Request**: `GetActiveSEOAddonsRequest`
- **Returns**: Array of active SEO add-ons with count property

## 4. Simultaneous Add and Favorite Functionality

### 4.1 Single Operation Capability

**Finding**: ✅ The `updateKeywords` API handles both keyword addition and favoriting in a single operation.

**Implementation Details**:
1. **Single API Call**: Only requires one call to `updateKeywords()`
2. **Automatic Favoriting**: Controlled by partner setting `isFavoriteNewKeywordsDisabled`
3. **Automatic Syncing**: Controlled by partner setting `isSyncKeywordsDisabled`
4. **No Race Conditions**: Sequential operations within single transaction

### 4.2 Partner Settings Control

**Location**: Partner settings are retrieved via `PartnerSettingsApiService.getPartnerSettings()`

**Key Settings**:
- `isFavoriteNewKeywordsDisabled`: Controls automatic favoriting of new keywords
- `isSyncKeywordsDisabled`: Controls automatic syncing of new keywords
- `canEditKeywords`: Controls whether keywords can be edited at all

**Configuration Location**: `apps/listing-builder-client/src/app/partner/partner-settings/partner-settings.component.html`

## 5. Current UI Implementation Analysis

### 5.1 Existing Add Keyword Dialog

**Location**: `libs/local-seo/src/lib/keyword-tracking-page/local-seo-keyword-dialog/local-seo-keyword-dialog.component.html`

**Key Features**:
- Displays current keyword usage with badge: `{{ currentKeywordUsage() }}/{{ keywordLimit }} {{ 'LOCAL_SEO.USED' | translate }}`
- Bulk input capability via `forms-va-input-repeated` component
- Smart suggestions table (paid edition only)
- Automatic keyword limit enforcement
- Add suggested keywords with single click

**Current Bulk Input Component**:
```html
<forms-va-input-repeated
  [controlArray]="form"
  [placeholder]="'LOCAL_SEO.ADD_A_KEYWORD' | translate"
  [label]="'LOCAL_SEO.KEYWORD.TITLE' | translate"
  [addText]="'LOCAL_SEO.ADD_A_KEYWORD' | translate"
  [maxFields]="keywordLimit - data?.keywords?.length"
  [disableAdd]="form.length >= keywordLimit"
  [bottomSpacing]="'small'"
  (removeClicked)="removeClicked($event)"
></forms-va-input-repeated>
```

### 5.2 Brand Keyword Tracking Component

**Location**: `apps/business-center-client/src/app/brands/brand-keyword-tracking/brand-keyword-tracking.component.html`

**Current State**: Read-only display of keyword tracking data with empty state message:
```html
<div class="subtitle">Add keywords in Local SEO to get started</div>
```

## 6. Implementation Recommendations

### 6.1 Recommended Approach

**Option 1: Reuse Existing Service and Dialog (Recommended)**
- Import `KeywordTrackingService` from `@vendasta/local-seo`
- Create a new bulk keyword dialog component in business-center-client
- Leverage existing `updateKeywords()` API
- Implement product edition detection for keyword limits

**Option 2: Create Business-Center Specific Service**
- Create wrapper service that calls local-seo APIs
- Implement business-center specific UI components
- Handle multi-business keyword addition

### 6.2 Required Components

1. **Bulk Keyword Input Dialog**
   - Multi-line text area or repeated input fields
   - Keyword limit display per business
   - Edition detection and limit enforcement
   - Validation and error handling

2. **Business Selection Interface**
   - Multi-select capability for businesses
   - Display keyword limits per business
   - Bulk operation confirmation

3. **Integration Service**
   - Wrapper around existing `KeywordTrackingService`
   - Handle business-specific agid resolution
   - Batch processing for multiple businesses

### 6.3 API Integration Pattern

```typescript
// Recommended implementation pattern
async addKeywordsToBusiness(businessId: string, keywords: string[]): Promise<void> {
  // 1. Get current keywords for business
  const currentKeywords = await this.getCurrentKeywords(businessId);
  
  // 2. Call existing updateKeywords API
  this.keywordTrackingService.updateKeywords(currentKeywords, keywords);
  
  // Note: Favoriting happens automatically based on partner settings
}
```

## 7. Technical Requirements

### 7.1 Dependencies

**Required Imports**:
```typescript
import { KeywordTrackingService } from '@vendasta/local-seo';
import { AccountsService } from '@vendasta/accounts/legacy';
import { 
  ListingProfileApiService,
  SEOApiService,
  PartnerSettingsApiService 
} from '@vendasta/listing-products';
```

### 7.2 Backend Changes

**✅ No backend changes required**:
- Existing APIs support bulk operations
- Partner settings already control auto-favoriting
- Keyword limits are properly enforced

### 7.3 Error Handling Considerations

1. **Keyword Limit Exceeded**: Current API handles this gracefully
2. **Duplicate Keywords**: Automatic deduplication in place
3. **Invalid Keywords**: Validation should be added at UI level
4. **Network Failures**: Existing error handling with snackbar notifications

## 8. UI/UX Considerations

### 8.1 Keyword Limit Display

**Current Implementation**: Badge display showing `X/Y USED`
**Recommendation**: 
- Display limits per business in multi-business view
- Color-coded indicators (green/yellow/red) based on usage
- Clear messaging when limits are exceeded

### 8.2 Bulk Operation Feedback

**Current Implementation**: Success/error snackbar notifications
**Recommendation**:
- Progress indicators for bulk operations
- Summary of successful/failed keyword additions
- Clear confirmation dialogs for bulk actions

### 8.3 Business Selection UX

**New Requirements**:
- Multi-select business interface
- Keyword limit awareness in selection
- Bulk operation confirmation with summary

## 9. Implementation Phases

### Phase 1: Core Functionality
1. Create bulk keyword input dialog
2. Integrate with existing `KeywordTrackingService`
3. Implement single-business keyword addition

### Phase 2: Multi-Business Support
1. Add business selection interface
2. Implement batch processing
3. Add progress indicators and error handling

### Phase 3: Enhanced UX
1. Add smart suggestions integration
2. Implement advanced validation
3. Add keyword analytics and insights

## 10. Risk Assessment

### Low Risk ✅
- Backend API compatibility
- Automatic favoriting functionality
- Keyword limit enforcement
- Product edition detection

### Medium Risk ⚠️
- Multi-business batch processing performance
- Error handling for partial failures
- UI complexity for business selection

### Mitigation Strategies
- Implement robust error handling
- Add comprehensive loading states
- Provide clear user feedback
- Test with various business configurations

## 11. Conclusion

The investigation reveals that the existing backend infrastructure fully supports bulk keyword addition and automatic favoriting functionality. The `updateKeywords` API in `KeywordTrackingService` can handle bulk operations efficiently, with automatic favoriting controlled by partner settings.

**Key Takeaways**:
- ✅ No backend changes required
- ✅ Existing APIs support bulk operations
- ✅ Automatic favoriting works out-of-the-box
- ✅ Keyword limits are properly enforced
- ✅ Product edition detection is implemented

**Next Steps**:
1. Design bulk keyword input UI for business-center-client
2. Implement business selection interface
3. Create integration service wrapper
4. Add comprehensive error handling and user feedback

The implementation should be straightforward, leveraging existing services and APIs while adding a new UI layer in the business-center-client application.
