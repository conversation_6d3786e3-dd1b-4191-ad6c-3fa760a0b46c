# Technical Spike: Bulk Keyword Addition and Favoriting Functionality

## Executive Summary

This technical spike investigates the implementation of bulk keyword addition and favoriting functionality from the business-center-client to the ML business app's keyword tracking system. The investigation covers backend API analysis, product edition detection, simultaneous add/favorite operations, and keyword limit display requirements.

## Investigation Findings

### 1. Backend API Analysis

#### Current `updateKeywords` API

**Location:** `libs/local-seo/src/lib/keyword-tracking-page/keyword-tracking/keyword-tracking.service.ts`

**API Contract:**
```typescript
updateKeywords(oldKeywords: string[], newKeywords: string[]): void
```

**Current Implementation:**
- **Method:** Uses `ListingProfileApiService.update()` with `UpdateListingProfileRequest`
- **Field Updated:** `richData.seoKeywords` via field mask `['seo_keywords']`
- **Merge Logic:** Combines old and new keywords using `Array.from(new Set(newKeywords.concat(oldKeywords)).values())`
- **Automatic Favoriting:** Automatically adds new keywords to favorites unless `partnerSettings.isFavoriteNewKeywordsDisabled` is true
- **Automatic Syncing:** Automatically adds new keywords to syncing unless `partnerSettings.isSyncKeywordsDisabled` is true

**Bulk Operation Support:**
✅ **SUPPORTS BULK OPERATIONS** - The API already accepts arrays of keywords and can handle multiple keywords in a single request.

**Response Handling:**
- Success: Shows "Keywords updated" snackbar message
- Error: Shows error message from API or generic "Unknown error"
- Updates local keyword store with new keywords, favorites, and syncing keywords

#### Current `updateFavoriteKeywords` API

**Location:** Same service file

**API Contract:**
```typescript
updateFavoriteKeywords(keywords: string[]): void
```

**Implementation:**
- **Method:** Uses `SEOApiService.saveSeoSettings()` with `SaveSEOSettingsRequest`
- **Field Updated:** `favoriteKeywords` via field mask `['favorite_keywords']`
- **Bulk Support:** ✅ Already accepts array of keywords

### 2. Product Edition Detection

#### Edition Detection Implementation

**Location:** `libs/local-seo/src/lib/keyword-tracking-page/keyword-tracking/keyword-tracking.service.ts`

**Current Method:**
```typescript
// Detects if account is free edition
this.isLBFreeEdition$ = this.agid$.pipe(
  switchMap((agid) => {
    const filters: ListAppAndAddonActivationStatusFilter = {
      appIds: ['MS'],
      statuses: [AppAndAddonActivationStatus.ACTIVATED, AppAndAddonActivationStatus.CANCELED],
    };
    return accountsService.listAppsAndAddonsActivationStatusesForBusiness(agid, filters).pipe(
      map((activations) => {
        if (activations?.length > 0) {
          switch (activations[0].editionId) {
            case DEMO_PAID_EDITION: // 'EDITION-MXWLTQPN'
              return false;
            case PROD_PAID_EDITION: // 'EDITION-CFH5CKHC'
              return false;
          }
        }
        return true; // Default to free edition
      }),
    );
  }),
);
```

**Product Editions:**
- **Free Edition:** Default when no paid edition detected
- **Demo Paid Edition:** `EDITION-MXWLTQPN`
- **Production Paid Edition:** `EDITION-CFH5CKHC`

**API Endpoint:** `AccountsService.listAppsAndAddonsActivationStatusesForBusiness()`

### 3. Simultaneous Add and Favorite Functionality

#### Current Behavior Analysis

**✅ ALREADY IMPLEMENTED** - The `updateKeywords` method automatically handles both adding keywords and favoriting them in a single operation:

```typescript
// From updateKeywords method (lines 661-662)
if (!partnerSettings?.isFavoriteNewKeywordsDisabled) {
  favoriteKeywords = Array.from(new Set(newKeywords.concat(favoriteKeywords)).values());
}
```

**Process Flow:**
1. Add keywords to `seoKeywords` via `ListingProfileService.update()`
2. Fetch partner settings to check if auto-favoriting is enabled
3. If enabled, automatically add new keywords to favorites
4. Update local keyword store with all changes

**No Race Conditions:** The operations are sequential and atomic within the same subscription chain.

### 4. Keyword Limit Display

#### Current Limit Calculation

**Location:** `libs/local-seo/src/lib/keyword-tracking-page/keyword-tracking/keyword-tracking.service.ts`

**Constants:**
```typescript
const free_edition_keywords = 3;
const paid_edition_keywords = 15;
const ADDON_INCREMENT = 15;
```

**Limit Calculation Logic:**
```typescript
this.keywordLimit$ = combineLatest([this.isLBFreeEdition$, this.addOns$]).pipe(
  map(([isFreeEdition, addOns]) => {
    let keywordLimit = free_edition_keywords; // 3

    if (!isFreeEdition) {
      keywordLimit = paid_edition_keywords; // 15
    }

    if (addOns?.length > 0) {
      const limitIncrement = addOns[0].count * ADDON_INCREMENT; // 15 per addon
      keywordLimit = keywordLimit + limitIncrement;
    }

    return keywordLimit;
  }),
);
```

**Keyword Limits by Edition:**
- **Free Edition:** 3 keywords
- **Paid Edition:** 15 keywords
- **With Add-ons:** +15 keywords per add-on activation

**Current UI Display:** Already implemented in `LocalSeoKeywordDialogComponent`:
```html
<glxy-badge [color]="currentKeywordUsage() < keywordLimit ? 'green' : 'grey'">
  {{ currentKeywordUsage() }}/{{ keywordLimit }} {{ 'LOCAL_SEO.USED' | translate }}
</glxy-badge>
```

## Implementation Recommendations

### 1. Reuse Existing APIs

**✅ RECOMMENDATION:** Use the existing `updateKeywords` API as-is for bulk operations.

**Rationale:**
- Already supports bulk keyword addition
- Automatically handles favoriting
- Includes proper error handling and user feedback
- No backend changes required

### 2. Business-Center-Client Integration

**Implementation Approach:**

1. **Create Bulk Keyword Service** in business-center-client:
   ```typescript
   // apps/business-center-client/src/app/brands/brand-keyword-tracking/bulk-keyword.service.ts
   @Injectable()
   export class BulkKeywordService {
     constructor(private keywordTrackingService: KeywordTrackingService) {}
     
     addBulkKeywords(businessIds: string[], keywords: string[]): Observable<void> {
       // Implementation for bulk operations across multiple businesses
     }
   }
   ```

2. **Create Bulk Keyword Dialog Component:**
   ```typescript
   // Similar to LocalSeoKeywordDialogComponent but for multiple businesses
   // apps/business-center-client/src/app/brands/brand-keyword-tracking/bulk-keyword-dialog/
   ```

3. **Integrate with Brand Keyword Tracking Component:**
   - Add bulk action button to the UI
   - Handle multiple business selection
   - Show progress indicators for bulk operations

### 3. Edition Detection for Business-Center-Client

**Approach:** Create a service that can detect editions for multiple businesses:

```typescript
@Injectable()
export class BusinessEditionService {
  getEditionsForBusinesses(businessIds: string[]): Observable<Map<string, EditionInfo>> {
    // Batch API calls to get edition info for multiple businesses
  }
  
  getKeywordLimitsForBusinesses(businessIds: string[]): Observable<Map<string, number>> {
    // Calculate keyword limits for each business based on their edition
  }
}
```

### 4. UI/UX Considerations

**Bulk Operation UI Requirements:**

1. **Business Selection:**
   - Multi-select checkbox for businesses
   - "Select All" functionality
   - Display current keyword count per business

2. **Keyword Input:**
   - Bulk text area for multiple keywords (one per line)
   - CSV import functionality
   - Keyword validation and deduplication

3. **Limit Validation:**
   - Show keyword limits per business
   - Prevent exceeding limits
   - Warning messages for businesses at/near limits

4. **Progress Tracking:**
   - Progress bar for bulk operations
   - Success/failure status per business
   - Detailed error reporting

## Backend Changes Required

**✅ NO BACKEND CHANGES REQUIRED**

The existing APIs fully support the required functionality:
- Bulk keyword addition via `updateKeywords`
- Automatic favoriting via partner settings
- Edition detection via accounts service
- Keyword limit calculation via existing logic

## Risk Assessment

### Low Risk Items
- ✅ API compatibility - existing APIs support bulk operations
- ✅ Edition detection - well-established pattern
- ✅ Keyword limits - existing calculation logic

### Medium Risk Items
- ⚠️ UI complexity for bulk operations across multiple businesses
- ⚠️ Performance with large numbers of businesses/keywords
- ⚠️ Error handling and user feedback for partial failures

### Mitigation Strategies
1. **Performance:** Implement batching and pagination for large operations
2. **Error Handling:** Provide detailed success/failure reporting per business
3. **UI Complexity:** Follow existing patterns from LocalSeoKeywordDialogComponent

## Conclusion

The investigation reveals that the existing backend APIs fully support the required bulk keyword addition and favoriting functionality. No backend modifications are needed. The main implementation effort will be in creating the business-center-client UI components and services to orchestrate bulk operations across multiple businesses while reusing the proven keyword tracking service patterns.

**Estimated Implementation Effort:** Medium (primarily frontend development)
**Backend Changes Required:** None
**Risk Level:** Low to Medium (mainly UI complexity)
