@import 'galaxy-base'; // only import once, in root scss

@import './app/material-overrides';

@import 'shimmer'; // stencil animation
@include va-stencil();

@import 'uikit_theme'; // handles the nav theme

$mobileResponsiveSize: 768px;

html {
  height: 100%;
  background-color: $lightest-grey;
}

body {
  height: 100%;
  position: relative;
  overflow: hidden;

  @media screen and (max-width: $mobileResponsiveSize) {
    height: unset;
    overflow: unset;
  }
}

h1 {
  // legacy page titles to be replaced with glxy-page
  color: $primary-text-color;
  font-size: 32px;
  font-weight: 300;
  line-height: 1.2;
  margin: 0 0 0.5em 0;
  clear: both;
}

// from uikit global, deprecated
.content {
  max-width: 1200px;
  padding: $spacing-3;
  margin: 0 auto;
}

@media (max-width: $mobileResponsiveSize) {
  // On mobile, this will scroll tables horizontally
  // that do not have a mobile responsive view
  .horizontal-scroller {
    overflow-x: auto;

    & > * {
      // affects only direct childen of this class
      display: block;
      min-width: 800px;
    }
  }
}

// Page Sizing

.ng-component-container {
  padding: 20px 20px 70px;
  min-height: 100%;
  background-color: #f6f6f6;
  //we need this extra container class because we still want the scroll bar to be the max width of the screen
  .ng-partner-central-standard-width {
    width: 980px; //avoid being unintentionally responsive for now, change to max-width some day
    margin: 0 auto;
  }
}

.uikit-page-container * .ng-component-container,
.uikit-page-container > .ng-component-container {
  height: 100% !important;
}

.va-page--fullwidth {
  .ng-partner-central-standard-width {
    width: 100%;
  }
}
.va-page--responsive {
  .ng-partner-central-standard-width {
    width: 100%;
    max-width: 1160px;
  }
}
.va-page--discover-products {
  .ng-partner-central-standard-width {
    width: 100%;
    max-width: 1600px;
  }
}
// overriding width caps for fullwidth pages (pages that can be >980px)
.pkg-calculator-page.va-page--fullwidth .ng-partner-central-standard-width {
  max-width: 1124px;
}

.mat-mdc-dialog-content uikit-search-select {
  display: block;
  margin-bottom: 16px;
}

._hj-f5b2a1eb-9b07_widget {
  right: 140px !important;
}

// Dragula styles
/* in-flight clone */
.gu-mirror {
  position: fixed !important;
  margin: 0 !important;
  z-index: 9999 !important;
  opacity: 0.8;
  -ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=80)';
  filter: alpha(opacity=80);
  pointer-events: none;
}

/* high-performance display:none; helper */
.gu-hide {
  left: -9999px !important;
}

/* added to mirrorContainer (default = body) while dragging */
.gu-unselectable {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

/* added to the source element while its mirror is dragged */
.gu-transit {
  opacity: 0.2;
  -ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=20)';
  filter: alpha(opacity=20);
}

// Styles for /users/user-bulk-actions-dialog
.bulk-update-user-permissions {
  .mat-mdc-dialog-container {
    padding: 0;
  }
}

// hide the zendesk help button when printing
#launcher {
  display: none;
}

// pulsating mic icon
.pulsating-circle {
  position: relative;
  float: right;
  width: 30px;
  height: 30px;

  &:before {
    content: '';
    position: relative;
    display: block;
    width: 200%;
    height: 200%;
    bottom: 9px;
    right: 9px;
    box-sizing: border-box;
    border-radius: 45px;
    background-color: $primary-accent-color;
    animation: pulse-ring 1.25s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
  }

  &:after {
    content: 'mic';
    position: absolute;
    right: 0;
    bottom: 0;
    display: block;
    width: 100%;
    height: 100%;
    background-color: white;
    border-radius: 15px;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
    animation: pulse-dot 1.25s cubic-bezier(0.455, 0.03, 0.515, 0.955) -0.4s infinite;
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.33);
  }
  80%,
  100% {
    opacity: 0;
  }
}

@keyframes pulse-dot {
  0% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1);
  }
  100% {
    transform: scale(0.8);
  }
}

@import 'shame';

.grecaptcha-badge {
  visibility: hidden;
}
