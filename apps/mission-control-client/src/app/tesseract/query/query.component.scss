@use 'design-tokens' as *;

mat-sidenav-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

mat-sidenav {
  width: 400px;

  b {
    margin-left: 10px;
  }
}

.main-content {
  flex-grow: 3;
  flex-shrink: 3;
  width: 100%;
  height: 100%;
  overflow: auto;
  position: relative;
  display: flex;
  flex-direction: column;
}

.sticky-footer {
  border-top: 1px solid $border-color;
  margin-top: auto;
}

.action-container {
  padding: $spacing-4;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.took {
  margin-left: 5px;
  font-size: 14px;
  color: $tertiary-font-color;
}

.quick-tip {
  font-size: 14px;

  strong {
    font-weight: 500;
  }
}

.query-results {
  border-top: 1px solid $border-color;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 100%;
  min-height: 50vh;
}

.results-table-wrapper {
  position: relative;
  overflow: auto;
  height: calc(100% - 16px);
  width: 100%;
  flex-grow: 3;
  flex-shrink: 3;

  .mat-mdc-row:hover {
    background-color: $warn-background-color;
  }

  .mat-mdc-cell,
  .mat-mdc-header-cell {
    padding-left: $spacing-4;
    padding-right: $spacing-4;
    border-right: 1px solid $border-color;
  }

  .mat-mdc-header-cell {
    border-right-color: $border-color;
    background-color: $primary-background-color;
  }

  .mat-mdc-cell {
    white-space: nowrap;
    font-family: $monospace-font-family;
    font-size: 13px;
  }

  // Adjust custom scroll bars
  &::-webkit-scrollbar {
    height: $spacing-2;
    width: $spacing-2;
    outline: 1px solid $border-color;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.26);
    border-radius: 0;

    :host-context(.glxy-dark-theme) & {
      background: rgba(255, 255, 255, 0.26);
    }
  }
}

.editor-view {
  height: initial !important;

  ::ng-deep {
    .CodeMirror {
      padding: $spacing-3;
      height: auto !important;
    }

    .CodeMirror-scroll {
      height: auto !important;
      min-height: 200px;
      max-height: 800px;
      overflow-y: hidden;
      overflow-x: auto;
    }
  }
}
