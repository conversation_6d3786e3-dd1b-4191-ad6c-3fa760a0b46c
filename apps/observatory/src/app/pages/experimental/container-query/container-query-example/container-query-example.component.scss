@use 'design-tokens' as *;

h4 {
  margin: 8px 16px 0 16px;
}
.glxy-resize {
  border: 1px solid $border-color;
  border-right: none;
}
.parent-container,
.parent-container-media {
  display: flex;
  flex-flow: row;
  min-height: 150px;
  margin: 8px 16px;
  transition: all 0.5s;

  .child-1,
  .child-5 {
    flex: 1;
    padding: 4px;
    color: $primary-text-color;
  }
  .child-2,
  .child-3,
  .child-4 {
    flex: 0 0 20px;
  }
  .child-1 {
    background: #99b898;
  }
  .child-2 {
    background: #fecea8;
  }
  .child-3 {
    background: #ff847c;
  }
  .child-4 {
    background: #e84a5f;
  }
  .child-5 {
    background: #2a363b;
  }

  &.glxy-bp-sm {
    flex-flow: column;

    .child-1,
    .child-5 {
      flex: 0 0 40px;
    }
  }
}
@media only screen and (max-width: 768px) {
  .parent-container-media {
    flex-flow: column;

    .child-1,
    .child-5 {
      flex: 0 0 40px;
    }
  }
}
