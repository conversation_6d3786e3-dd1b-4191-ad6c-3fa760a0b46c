@use '../../core/breaks.scss' as breaks;
@use 'design-tokens' as *;
@import url('https://www.cdnstyles.com/static/css/thermometer.css');

body {
  font-family: tahoma, helvetica, arial;
  background: #fff;
  margin: 0px;
  color: #666666;
  font-size: 16px;
  text-align: center;
}

.section-container {
  border: 1px solid #eee;
  margin-bottom: 10px;
  min-height: 200px;
  page-break-after: always;
  float: none;
}

.last-section-container {
  page-break-after: auto;
}

.section-container h3 {
  background-color: #d9eef0;
  /*color: #fff;*/
  margin: 0px;
  padding: 5px 10px;
  border-bottom: 1px solid #eee;
  float: none;
}

.industry-averages {
  width: 175px;
  float: right;
  line-height: 2em;
  font-size: 12px;
  color: grey;
  font-weight: 400;
}

.average {
  float: right;
  font-weight: bold;
}

.listing-point-score-section {
  width: 15%;
  float: left;
}

.letter-grade-container {
  width: 100px;
  height: 100px;
  margin: 43px auto 0;
}

.letter-grade-container[disabled] {
  margin-top: 0;
}

.listing-point-score-container {
  margin: 20px;
  height: 250px;
}

.listing-point-score-container.thermometer-disabled {
  margin: 20px;
  height: 140px;
}

.no-industry-data {
  line-height: 100px;
  text-align: center;
}

.letter-grade-disabled {
  background-color: lightgrey;
}

.thermometer {
  width: 75%;
  float: right;
}

.delta-neutral {
  color: #999;
}

.delta-positive {
  color: #3cad29;
}

.delta-positive > i {
  vertical-align: middle;
}

.delta-negative {
  color: #f5230b;
}

.delta-negative > i {
  vertical-align: middle;
}

.no-change {
  color: #9e9e9e;
  font-style: italic;
  font-size: 14px;
}

.listing-point-score {
  font-size: 28px;
  text-align: center;
  margin-top: 20px;
}

.listing-point-score-delta {
  font-size: 14px;
  text-align: center;
}

.listing-point-score-disabled {
  color: #999;
}

.listing-point-score-A {
  color: #44b046;
}

.listing-point-score-B {
  color: #87c387;
}

.listing-point-score-C {
  color: #f2ad29;
}

.listing-point-score-D {
  color: #e95d5f;
}

.listing-point-score-F {
  color: #c73033;
}

.listings-description {
  border-bottom: 0;
}

.listings-description .description-text {
  padding: 10px 20px 10px 20px;
}

.logo-marker {
  position: relative;
  top: -25px;
  max-width: 100%;
}

.letter-grade-container > .letter,
.thermometer > .section,
.thermometer > .marker {
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
}

@media (max-width: 600px) {
  mat-card {
    min-height: 500px;
  }
  .listing-point-score-container {
    min-height: 410px;
    text-align: center;
    padding: 10px 0 10px 0;
    margin: 10px 0 25px 0;
    width: 100%;
  }
  .listing-point-score-section {
    width: 100%;
    margin-bottom: 32px;
  }
  .letter-grade-container {
    margin-top: 0;
  }
  .thermometer {
    text-align: center;
    width: 100%;
  }
}

$STACKED_GRADE_GRAPH: 601px;
.thermometer {
  .score-label {
    display: block;
    font-weight: 500;
    white-space: normal;

    @media only screen and (min-width: $STACKED_GRADE_GRAPH) {
      white-space: nowrap;
    }
  }
  .marker {
    background: transparent;
  }
  .marker:after {
    background: $primary-background-color;
  }

  .hoverable {
    color: $primary-font-color;
    border-bottom: 1px dotted $tertiary-font-color;
  }
  .marker-top {
    min-width: fit-content;
  }
}

.thermometer-inputs {
  --width-F: 40%;
  --width-D: 10%;
  --width-C: 25%;
  --width-B: 15%;
  --width-A: 10%;
  --percentile-D: '40th';
  --percentile-C: '50th';
  --percentile-B: '75th';
  --percentile-A: '90th';
}

.exec-report-card {
  background: transparent;
  padding: 16px 128px;
  border: none;

  @include breaks.respond-to(mobile) {
    padding: 16px 8px;
  }
}
