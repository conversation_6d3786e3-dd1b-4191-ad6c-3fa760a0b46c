import { inject, InjectionToken } from '@angular/core';
import {
  AvailableFields,
  FormBuilderDependencies,
  FormPreview,
  MappedFieldService,
  UnmappedFieldService,
} from '@galaxy/form-builder';
import { FormConfigFieldInterface, FormsApiService, JsonSchemaLibrary } from '@vendasta/forms_microservice';
import { AccountGroupService } from '../account-group';
import { map, Observable, of, switchMap } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { AccountGroupApiService } from '@vendasta/account-group';
import { partnerId } from '../../globals';
import { FeatureFlagService } from '../core/feature-flag.service';

export const FormBuilderInjectionToken = new InjectionToken<FormBuilderDependencies>(
  'dependencies given to form builder library',
  {
    factory: () => {
      const formService: FormsApiService = inject(FormsApiService);
      const accountGroupService = inject(AccountGroupService);
      const unmappedFieldService: UnmappedFieldService = inject(UnmappedFieldService);
      const mappedFieldService: MappedFieldService = inject(MappedFieldService);
      const accountGroupApiService = inject(AccountGroupApiService);
      const featureFlagService = inject(FeatureFlagService);

      mappedFieldService.namespace = accountGroupService.currentAccountGroupId$;
      return {
        routePrefix$: accountGroupService.currentAccountGroupId$.pipe(
          map((accountGroupId) => `/account/location/${accountGroupId}/custom-forms`),
        ),
        namespace$: accountGroupService.currentAccountGroupId$,
        parentNamespace$: accountGroupService.currentAccountGroupId$.pipe(
          switchMap((accountGroupId) =>
            accountGroupApiService.getMulti({
              accountGroupIds: [accountGroupId],
              projectionFilter: { accountGroupExternalIdentifiers: true },
            }),
          ),
          map((response) => response.accountGroups[0]?.accountGroup?.accountGroupExternalIdentifiers?.partnerId),
        ),
        schemaLibrary: JsonSchemaLibrary.JSON_SCHEMA_LIBRARY_JSONFORM,
        hasSections$: featureFlagService.checkFeatureFlag(partnerId, '', 'custom_form_section'),

        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        listFields: (search: string, cursor: string, pageSize: number): Observable<AvailableFields[]> => {
          const genericFields = unmappedFieldService.getUnmappedFields(search);

          return mappedFieldService.buildMappedFields(search).pipe(
            map((results) => {
              return [
                genericFields.find((group) => group.labelKey === 'CUSTOM_FORMS.LIST_OF_FIELDS.ELEMENT'),
                ...results,
                genericFields.find((group) => group.labelKey === 'CUSTOM_FORMS.LIST_OF_FIELDS.GENERIC'),
              ];
            }),
          );
        },
        previewForm(formFields: FormConfigFieldInterface[], submitButtonLabel?: string): Observable<FormPreview> {
          return formService
            .previewForm({
              formConfig: {
                fields: formFields,
                submitButtonLabel: submitButtonLabel || '',
              },
              library: this.schemaLibrary,
            })
            .pipe(
              catchError((err) => {
                console.error('error fetching preview for form', err);
                return of({ jsonSchema: '{}', jsonUiSchema: '[]' });
              }),
            );
        },
      } as FormBuilderDependencies;
    },
  },
);
