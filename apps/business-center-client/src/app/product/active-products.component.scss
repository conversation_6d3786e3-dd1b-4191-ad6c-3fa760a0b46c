@use 'design-tokens' as *;
@use '../core/breaks.scss' as breaks;
@use '../core/easings.scss' as easings;

@keyframes product-slide-in {
  0% {
    opacity: 0;
    padding-right: 16px;
  }
  100% {
    opacity: 1;
    padding-right: 0;
  }
}

va-icon {
  display: block;
  margin-bottom: 8px;

  ::ng-deep .va-icon-container {
    margin: 0 auto;
  }
}

.products {
  padding-bottom: 16px;
}

.product {
  min-height: 240px;
  overflow: hidden;
  opacity: 0;
  animation: product-slide-in 0.5s forwards easings.$PRODUCT_CARD_EASING;

  @include breaks.respond-to(mobile) {
    opacity: 1;
    animation: none;
    overflow: visible;
  }

  .more-products-card {
    height: 100%;
    padding: 16px;

    @include breaks.respond-to(mobile) {
      width: 100%;
      margin: auto;
      padding: 0;
    }
  }
}

.projects {
  .project-milestone {
    overflow: hidden;
    opacity: 0;
    animation: product-slide-in 0.5s forwards easings.$PRODUCT_CARD_EASING;

    @include breaks.respond-to(mobile) {
      opacity: 1;
      animation: none;
      overflow: visible;
    }
  }
}

// Product stylings
.product:nth-of-type(1) {
  // No animation delay
}
.product:nth-of-type(2),
.product:nth-of-type(5) {
  animation-delay: 0.1s;
}
.product:nth-of-type(3),
.product:nth-of-type(6),
.product:nth-of-type(9) {
  animation-delay: 0.2s;
}
.product:nth-of-type(4),
.product:nth-of-type(7),
.product:nth-of-type(10),
.product:nth-of-type(13) {
  animation-delay: 0.3s;
}
.product:nth-of-type(8),
.product:nth-of-type(11),
.product:nth-of-type(14) {
  animation-delay: 0.4s;
}
.product:nth-of-type(12),
.product:nth-of-type(15) {
  animation-delay: 0.5s;
}
.product:nth-of-type(16) {
  animation-delay: 0.6s;
}
.product:nth-of-type(n + 17) {
  animation-delay: 0.6s;
}

.disabled-product {
  opacity: 0.5;
}

.product-traditional-view {
  min-width: 168px;
  padding: 0 8px;
  margin-bottom: 24px;
  text-align: center;

  a {
    display: block;
    position: relative;
    transition: all 0.3s ease-in-out;
  }

  a:hover {
    transform: scale(1.1);
  }
  .product-badge {
    position: relative;
    color: white;
    padding: 0 8px;
    font-weight: 500;
    font-size: 11px;
    line-height: 17px;
    top: -1.5em;
    margin-bottom: -1.5em;

    &:after {
      border-radius: 10px;
      display: inline-block;
      padding: 0 8px;
    }
    &.limited-preview {
      &:after {
        background: #1186bb;
        content: 'LIMITED PREVIEW';
      }
    }

    &.trial {
      &:after {
        background: #c6252e;
        content: 'TRIAL';
      }
    }

    &.new {
      &:after {
        background: $green;
        content: 'NEW!';
      }
    }
  }

  .product-name {
    color: $secondary-text-color;
  }
}

.suspended-message {
  display: flex;
  justify-content: center;
  padding-top: 32px;
}

.store-link {
  text-align: right;
}

.more-products-card {
  text-align: center;
  display: flex;
  flex-flow: column nowrap;
  align-items: center;
  justify-content: center;
}

.more-products-icon {
  height: 80px;
  width: 80px;
  background-color: $secondary-background-color;
  border-radius: 50%;
  margin: 0 auto;
  padding-top: 6px;
  text-align: center;
  font-size: 60px;
  color: $secondary-text-color;
  div {
    line-height: 1;
  }
}

.product-name {
  margin-top: 8px;
}

@media only screen and (max-width: 767px) {
  .product {
    width: 100%;
    min-height: auto;
    margin: 0;
    margin-top: 8px;
  }
}

.project-tracker-sidenav-container {
  z-index: 1002; // to overlap BC's top bar which has 1001 and keep the same value in all centers
  position: sticky;
}
