@use 'design-tokens' as *;
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap');

.compose-button {
  margin-right: $spacing-2;
}

.menu-desc {
  color: $secondary-text-color;
  font-size: 11px;
  font-weight: 400;
  font-family: 'Roboto';
}

.grey-line {
  border: none;
  border-top: 1px solid;
  border-color: $border-color;
  margin: 1px 0;
}

.item-container {
  display: flex;
  align-items: center;
}

.single-post-button {
  width: 100%;
}

.calendar-flow-container {
  padding-bottom: 12px;
}

.item-header {
  border-bottom: $border-color;
}

.ai-create {
  gap: 5px;
}

.item-section {
  font-size: 10px;
  font-weight: 500;
  letter-spacing: 0.1px;
  padding: 16px 16px 8px 16px;
  gap: 10px;
}

.disable-button {
  opacity: 50%;
}

.min-height {
  min-height: 70px;
}

.pro-badge {
  margin-right: 5px;
  font-weight: 700;
}

.drop-menu {
  min-width: 285px !important;
}

mat-icon {
  color: $secondary-text-color !important;
  margin-right: 10px !important;
}

.title {
  font-weight: 700;
  font-size: 14px;
  line-height: 16px;
  font-family: 'Roboto';
}
