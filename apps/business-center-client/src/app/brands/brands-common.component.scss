@use 'design-tokens' as dt;
@use '../core/breaks.scss' as bp;

.multi-location-analytics {
  .header {
    @include bp.respond-to(mobile) {
      margin-top: 40px;
    }

    h1 {
      margin: 0;

      @include bp.respond-to(mobile) {
        font-size: 16px;
      }
    }
  }

  .group-nav {
    // Align with time selector
    padding-top: dt.$spacing-2;
  }

  .group-nav-component {
    white-space: nowrap;
  }

  .mat-mdc-card-title h1 {
    margin: 0;
  }

  .mat-mdc-card-subtitle {
    color: rgba(0, 0, 0, 0.54);
  }

  .mat-mdc-card-actions .mat-button-wrapper {
    display: flex;
    align-items: center;
  }

  .card-row {
    margin-bottom: 16px;
  }

  .path-nav-button {
    // match the path labels beside to prevent movement when gone/present
    height: 32px;
    width: 32px;
    line-height: 32px;
  }

  .delta-positive {
    color: dt.$green;
  }

  .delta-negative {
    color: dt.$red;
  }

  .delta-text {
    font-size: 12px;
    vertical-align: top;
    line-height: 24px;
  }

  .delta-triangle {
    margin-left: -6px;
  }

  .no-change {
    color: dt.$gray;
    font-style: italic;
    margin-top: 4px; // match change value spacing
    white-space: nowrap;
    padding-left: 4px;
  }

  .trend-and-map-section {
    padding: 0;
  }

  .trend-section {
    position: relative;
    min-width: 400px;

    @include bp.respond-to-down(mobile) {
      min-width: auto;
    }

    .error-message {
      position: absolute;
      height: 90px;
      width: 350px;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      margin: auto;

      mat-icon {
        display: block;
        text-align: center;
        margin: auto;
        font-size: 40px;
        height: 40px;
        width: 40px;
      }

      span {
        display: block;
        text-align: center;
      }
    }
  }
}

.spacing {
  margin-top: 40px;
}

.trend-section {
  display: flex;
  flex-flow: row;
  flex-wrap: wrap;
  width: 100%;
}

.metric-switcher {
  margin-right: dt.$spacing-2;
}

.mobile-hidden {
  @include bp.respond-to-down(mobile) {
    display: none;
  }
}

.chart-header {
  flex-basis: 100%;
}

.info-box {
  border-radius: 4px;
  background-color: #efefef;
  border: 1px solid #c5c5c5;
  padding: 24px 32px;
  display: flex;
  align-items: center;

  .title {
    font-weight: 500;
    font-size: 20px;
    line-height: 32px;
    margin: 0 0 4px;
  }

  .instructions {
    color: dt.$secondary-text-color;
  }

  .mat-icon {
    margin: 0 32px 0 0;
    font-size: 64px;
    height: auto;
    width: auto;
    color: #1572bd;
    opacity: 0.7;
  }
}
