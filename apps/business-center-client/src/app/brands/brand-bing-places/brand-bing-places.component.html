<glxy-page>
  <glxy-page-toolbar>
    <glxy-page-title>{{ 'PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.BING_PLACES' | translate }}</glxy-page-title>
    <glxy-page-actions>
      <bc-time-range-picker></bc-time-range-picker>
      <bc-brand-filter-container></bc-brand-filter-container>
    </glxy-page-actions>
  </glxy-page-toolbar>
  <glxy-page-below-toolbar>
    <bc-nav-tabs></bc-nav-tabs>
  </glxy-page-below-toolbar>
  <div class="multi-location-analytics">
    <div
      class="date-range-header"
      [matTooltip]="'PERFORMANCE.MULTI_LOCATION.BING.DELAY_TOOLTIP' | translate"
      [matTooltipPosition]="'right'"
    >
      {{ bingPlacesService.queryDateString$ | async }}<sup>*</sup>
    </div>
    <div class="card-row">
      <bc-brand-compare
        [tableColumns$]="tableColumns$ | async"
        [brandRows]="loadedMapLocations$ | async"
        [measureSelected]="'total_views'"
        [cardConfig]="cardConfig$ | async"
        [dataSource]="dataSource$ | async"
        (measureSelectedChange)="onMeasureSelected($event)"
      ></bc-brand-compare>
    </div>
  </div>
</glxy-page>
