import { AsyncPipe } from '@angular/common';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit, signal } from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { Observable, combineLatest, switchMap, zip } from 'rxjs';
import { filter, map, shareReplay, startWith } from 'rxjs/operators';
import { AccountGroup, getLocation } from '../../account-group/account-group';
import { LocationsService } from '../../locations';
import { AccountGroupMetricService } from '../../metrics/account-group.service';
import { GmbService, InsightsData } from '../../metrics/gmb.service';
import { StatByTime } from '../../metrics/query.service';
import { NavigationService } from '../../navigation/navigation.service';
import { Mode, SidepanelService, Size } from '../../navigation/sidepanel.service';
import {
  CardDataContainer,
  CardMultiSourceDataConfig,
  MultiSeriesChartType,
} from '../../performance-dashboard/cards/interface';
import { ConnectedCardsService } from '../../performance-dashboard/connected-cards/connected-cards.service';
import { TimeRangePickerComponent } from '../../shared/time-range-picker.component';
import { TimeRangeService } from '../../shared/time-range.service';
import { CompareTab } from '../brand-compare/brand-compare.component';
import { BrandCompareModule } from '../brand-compare/brand-compare.module';
import { BrandFilterContainerModule } from '../brand-filter-container/brand-filter-container.module';
import { computeGradesFromMeasures } from '../grades';
import { BrandRow, MeasureValueMap, MetricColumn } from '../table/table.service';
import { MultiLocationService } from './../multi-location.service';
import { BrandGmbSidebarComponent } from './brand-gmb-sidebar/brand-gmb-sidebar.component';
import { BrandGMBSidebarModule } from './brand-gmb-sidebar/brand-gmb-sidebar.module';
import { NavTabsComponent } from '@vendasta/business-nav';

@Component({
  selector: 'bc-brand-gmb',
  templateUrl: './brand-gmb.component.html',
  styleUrls: ['./brand-gmb.component.scss', './../brands-common.component.scss'],
  imports: [
    AsyncPipe,
    BrandCompareModule,
    BrandGMBSidebarModule,
    MatTooltipModule,
    GalaxyPageModule,
    TranslateModule,
    TimeRangePickerComponent,
    BrandFilterContainerModule,
    NavTabsComponent,
  ],
})
export default class BrandGMBComponent implements OnInit, OnDestroy {
  private readonly measureSelected = signal<string>('total_views');
  public readonly measureSelected$ = toObservable(this.measureSelected);
  compareTabs$: Observable<CompareTab[]>;
  insightsMeasureMap: Observable<{ [key: string]: MeasureValueMap }>;
  gradeMap$: Observable<{ [key: string]: string }>;
  mapLocations$: Observable<BrandRow[]>;
  loadedMapLocations$: Observable<BrandRow[]>;
  cardConfig$: Observable<CardMultiSourceDataConfig>;
  dataSource$: Observable<CardDataContainer>;

  tableColumns$: Observable<MetricColumn[]>;

  constructor(
    private nav: NavigationService,
    public multiLocationService: MultiLocationService,
    public sidepanelService: SidepanelService,
    private locationsService: LocationsService,
    public gmbService: GmbService,
    private timeRangeService: TimeRangeService,
    private accountGroupMetricService: AccountGroupMetricService,
    private connectedCardsService: ConnectedCardsService,
  ) {}

  ngOnInit(): void {
    this.sidepanelService.setView(Size.LARGE, Mode.SIDE, BrandGmbSidebarComponent);
    this.connectedCardsService.forceMobile(true);
    this.multiLocationService.setMetricCategory('GMB');

    this.nav.setBreadcrumbs([{ text: 'PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_MY_BUSINESS' }]);

    this.compareTabs$ = combineLatest([
      this.gmbService.currentOverallInsights$,
      this.gmbService.previousOverallInsights$,
      this.gmbService.currentOverallSearchKeywords$,
      this.gmbService.previousOverallSearchKeywords$,
    ]).pipe(
      map(([insights, pInsights, searchKeywords, pSearchKeywords]) => {
        if (!insights) {
          return [
            {
              titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.TOTAL_VIEWS',
              measureKey: 'total_views',
            },
            {
              titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.SEARCHES',
              measureKey: 'searches',
            },
            {
              titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.TOTAL_ACTIONS',
              measureKey: 'total_actions',
            },
          ];
        }
        let deltaViews, deltaActions, deltaSearches;
        if (pInsights) {
          deltaViews = insights.totalViews() - pInsights.totalViews();
          deltaSearches = searchKeywords - pSearchKeywords;
          deltaActions = insights.totalActions() - pInsights.totalActions();
        }
        const tabs = [];
        tabs.push({
          titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.TOTAL_VIEWS',
          measureKey: 'total_views',
          value: insights.totalViews(),
          deltaAbs: deltaViews,
          deltaRel: 0,
          deltaGood: deltaViews > 0,
          showDeltaAbs: true,
        });
        tabs.push({
          titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.SEARCHES',
          tooltipTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.SEARCHES_TOOLTIP',
          measureKey: 'searches',
          value: searchKeywords,
          deltaAbs: deltaSearches,
          deltaRel: 0,
          deltaGood: deltaSearches > 0,
          showDeltaAbs: true,
        });
        tabs.push({
          titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.TOTAL_ACTIONS',
          measureKey: 'total_actions',
          value: insights.totalActions(),
          deltaAbs: deltaActions,
          deltaRel: 0,
          deltaGood: deltaActions > 0,
          showDeltaAbs: true,
        });
        return tabs;
      }),
      startWith([
        {
          titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.TOTAL_VIEWS',
          measureKey: 'total_views',
        },
        {
          titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.TOTAL_SEARCHES',
          measureKey: 'searches',
        },
        {
          titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.TOTAL_ACTIONS',
          measureKey: 'total_actions',
        },
      ]),
    );

    // Build measure value map for reviews
    this.insightsMeasureMap = this.locationsService.currentAccountGroupIds$.pipe(
      switchMap((accountGroupIds) => {
        return zip([
          this.gmbService.currentInsightsByLocation$,
          this.gmbService.previousInsightsByLocation$,
          this.gmbService.currentSearchKeywordsByLocation$,
          this.gmbService.previousSearchKeywordsByLocation$,
        ]).pipe(
          map(([insightStats, pInsightStats, searchKeywords, pSearchKeywords]) => {
            const locationMeasureMap = {};
            if (accountGroupIds == null || insightStats == null) {
              return locationMeasureMap;
            }

            accountGroupIds.forEach((accountGroupId) => {
              // Default value all locations
              locationMeasureMap[accountGroupId] = {
                total_views: { value: null },
                total_actions: { value: null },
                views_maps: { value: null },
                views_search: { value: null },
                actions_website: { value: null },
                actions_phone: { value: null },
                actions_driving_directions: { value: null },
                actions_bookings: { value: null },
                actions_conversations: { value: null },
                actions_food_orders: { value: null },
                searches: { value: null },
              };
            });
            insightStats.forEach((insightStat) => {
              // Set value for all locations in stats
              locationMeasureMap[insightStat.dimension] = {
                total_views: { value: insightStat.stat.totalViews() },
                total_actions: { value: insightStat.stat.totalActions() },
                views_maps: { value: insightStat.stat.views_maps },
                views_search: { value: insightStat.stat.views_search },
                actions_website: { value: insightStat.stat.actions_website },
                actions_phone: { value: insightStat.stat.actions_phone },
                actions_driving_directions: { value: insightStat.stat.actions_driving_directions },
                actions_bookings: { value: insightStat.stat.actions_bookings },
                actions_conversations: { value: insightStat.stat.actions_conversations },
                actions_food_orders: { value: insightStat.stat.actions_food_orders },
                searches: { value: 0 },
              };
            });
            searchKeywords?.forEach((searchKeyword) => {
              locationMeasureMap[searchKeyword.dimension].searches = { value: searchKeyword.stat };
            });
            if (pInsightStats) {
              pInsightStats.forEach((pInsightStat) => {
                locationMeasureMap[pInsightStat.dimension]['total_views'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['total_views'].value || 0) -
                  pInsightStat.stat.totalViews();
                locationMeasureMap[pInsightStat.dimension]['total_actions'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['total_actions'].value || 0) -
                  pInsightStat.stat.totalActions();
                locationMeasureMap[pInsightStat.dimension]['views_maps'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['views_maps'].value || 0) - pInsightStat.stat.views_maps;
                locationMeasureMap[pInsightStat.dimension]['views_search'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['views_search'].value || 0) -
                  pInsightStat.stat.views_search;
                locationMeasureMap[pInsightStat.dimension]['actions_website'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['actions_website'].value || 0) -
                  pInsightStat.stat.actions_website;
                locationMeasureMap[pInsightStat.dimension]['actions_phone'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['actions_phone'].value || 0) -
                  pInsightStat.stat.actions_phone;
                locationMeasureMap[pInsightStat.dimension]['actions_driving_directions'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['actions_driving_directions'].value || 0) -
                  pInsightStat.stat.actions_driving_directions;
                locationMeasureMap[pInsightStat.dimension]['actions_bookings'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['actions_bookings'].value || 0) -
                  pInsightStat.stat.actions_bookings;
                locationMeasureMap[pInsightStat.dimension]['actions_conversations'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['actions_conversations'].value || 0) -
                  pInsightStat.stat.actions_conversations;
                locationMeasureMap[pInsightStat.dimension]['actions_food_orders'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['actions_food_orders'].value || 0) -
                  pInsightStat.stat.actions_food_orders;
              });
            }
            if (pSearchKeywords) {
              pSearchKeywords.forEach((pSearchKeyword) => {
                locationMeasureMap[pSearchKeyword.dimension]['searches'].deltaAbs =
                  (locationMeasureMap[pSearchKeyword.dimension]['searches'].value || 0) - pSearchKeyword.stat;
              });
            }

            return locationMeasureMap;
          }),
        );
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

    this.gradeMap$ = combineLatest([this.measureSelected$, this.insightsMeasureMap]).pipe(
      map(([measureSelected, measureMap]) => {
        if (measureMap == null) {
          return null;
        }
        const [locationGrades] = computeGradesFromMeasures(measureMap, measureSelected);
        return locationGrades;
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

    // Progressively loaded data for map/table. Location data, then measures/grades
    this.mapLocations$ = combineLatest([
      this.accountGroupMetricService.filteredLocationsForPath$,
      this.insightsMeasureMap,
      this.gradeMap$, // Grades strictly driven from measures, probably causes some wasted UI work to fire twice
    ]).pipe(
      map(([locations, reviewMeasures, gradeMap]) => {
        if (locations == null) {
          return null;
        }

        return Object.keys(locations)
          .map((k) => locations[k])
          .map((ag: AccountGroup) => {
            return {
              accountGroup: ag,
              title: ag.companyName,
              subtitle: getLocation(ag),
              grade: (gradeMap || {})[ag.accountGroupId] || 'Loading',
              measureMap: (reviewMeasures || {})[ag.accountGroupId] || {},
            };
          });
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

    this.loadedMapLocations$ = this.mapLocations$.pipe(filter((locs) => !!locs));

    const chartConfig: CardMultiSourceDataConfig = {
      dataDisplayType: MultiSeriesChartType.LineWithFill,
      dataTitleTranslationKey: 'COMMON.TREND.12_MONTHS',
      chartConfig: {
        chartDataType: 'date',
        showLegend: true,
        colorStepOverride: 0,
        animations: false,
        yAxisTickFormatting: 'whole-number',
        formatting: 'day',
        zeroOnlyValuesAllowed: false,
      },
    };
    this.cardConfig$ = this.timeRangeService.formattingOption$.pipe(
      map((formatting) => {
        chartConfig.chartConfig.formatting = formatting;
        return chartConfig;
      }),
      startWith(chartConfig),
    );

    this.dataSource$ = combineLatest([
      this.measureSelected$,
      this.gmbService.currentInsightsByTime$,
      this.gmbService.currentSearchesByTime$,
    ]).pipe(
      map(([measureSelected, statsByTime, searchKeywordsByTime]) => {
        if (!statsByTime) {
          return {
            chartData: undefined,
          };
        }
        if (measureSelected === 'total_views') {
          return {
            chartData: [
              {
                title: 'PERFORMANCE.MULTI_LOCATION.GMB.MAP_VIEW',
                data: statsByTime.map((statByTime) => {
                  return {
                    name: statByTime.localAlignedTime,
                    value: statByTime.stat.views_maps,
                  };
                }),
              },
              {
                title: 'PERFORMANCE.MULTI_LOCATION.GMB.SEARCH_VIEW',
                data: statsByTime.map((statByTime) => {
                  return {
                    name: statByTime.localAlignedTime,
                    value: statByTime.stat.views_search,
                  };
                }),
              },
            ],
          };
        } else if (measureSelected === 'searches') {
          // distribute the statistic evenly over selected dates.
          if (searchKeywordsByTime.length === 1 && searchKeywordsByTime?.[0]?.stat) {
            searchKeywordsByTime = distributeStatOverSelectedDates(statsByTime, searchKeywordsByTime[0].stat);
          }
          return {
            chartData: [
              {
                title: 'PERFORMANCE.MULTI_LOCATION.GMB.SEARCHES',
                data: searchKeywordsByTime.map((datapoint) => {
                  return {
                    name: datapoint.localAlignedTime,
                    value: datapoint.stat,
                  };
                }),
              },
            ],
          };
        } else {
          return {
            chartData: [
              {
                title: 'PERFORMANCE.MULTI_LOCATION.GMB.WEBSITE_ACTION',
                data: statsByTime.map((statByTime) => {
                  return {
                    name: statByTime.localAlignedTime,
                    value: statByTime.stat.actions_website,
                  };
                }),
              },
              {
                title: 'PERFORMANCE.MULTI_LOCATION.GMB.PHONE_ACTION',
                data: statsByTime.map((statByTime) => {
                  return {
                    name: statByTime.localAlignedTime,
                    value: statByTime.stat.actions_phone,
                  };
                }),
              },
              {
                title: 'PERFORMANCE.MULTI_LOCATION.GMB.DIRECTIONS_ACTION',
                data: statsByTime.map((statByTime) => {
                  return {
                    name: statByTime.localAlignedTime,
                    value: statByTime.stat.actions_driving_directions,
                  };
                }),
              },
              {
                title: 'PERFORMANCE.MULTI_LOCATION.GMB.BOOKINGS_ACTION',
                data: statsByTime.map((statByTime) => {
                  return {
                    name: statByTime.localAlignedTime,
                    value: statByTime.stat.actions_bookings,
                  };
                }),
              },
              {
                title: 'PERFORMANCE.MULTI_LOCATION.GMB.CONVERSATIONS_ACTION',
                data: statsByTime.map((statByTime) => {
                  return {
                    name: statByTime.localAlignedTime,
                    value: statByTime.stat.actions_conversations,
                  };
                }),
              },
              {
                title: 'PERFORMANCE.MULTI_LOCATION.GMB.FOOD_ORDERS_ACTION',
                data: statsByTime.map((statByTime) => {
                  return {
                    name: statByTime.localAlignedTime,
                    value: statByTime.stat.actions_food_orders,
                  };
                }),
              },
            ],
          };
        }
      }),
      startWith({
        chartData: undefined,
      }),
    );

    // GMB columns always contain the totals of views/actions, and will contain the subtotals
    // of the selected table to the right of the relevant total
    this.tableColumns$ = this.measureSelected$.pipe(
      map(() => {
        const columns = [];
        columns.push({
          headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.TOTAL_VIEWS',
          measureKey: 'total_views',
          metaValueList: ['mean', 'median', 'sum'],
          metaValueDefault: 'sum',
        });
        columns.push({
          headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.MAP_VIEW',
          measureKey: 'views_maps',
          metaValueList: ['mean', 'median', 'sum'],
          metaValueDefault: 'sum',
        });
        columns.push({
          headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.SEARCH_VIEW',
          measureKey: 'views_search',
          metaValueList: ['mean', 'median', 'sum'],
          metaValueDefault: 'sum',
        });
        columns.push({
          headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.SEARCHES',
          measureKey: 'searches',
          metaValueList: ['mean', 'median', 'sum'],
          metaValueDefault: 'sum',
        });
        columns.push({
          headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.TOTAL_ACTIONS',
          measureKey: 'total_actions',
          metaValueList: ['mean', 'median', 'sum'],
          metaValueDefault: 'sum',
        });
        columns.push({
          headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.WEBSITE_ACTION',
          measureKey: 'actions_website',
          metaValueList: ['mean', 'median', 'sum'],
          metaValueDefault: 'sum',
        });
        columns.push({
          headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.PHONE_ACTION',
          measureKey: 'actions_phone',
          metaValueList: ['mean', 'median', 'sum'],
          metaValueDefault: 'sum',
        });
        columns.push({
          headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.DIRECTIONS_ACTION',
          measureKey: 'actions_driving_directions',
          metaValueList: ['mean', 'median', 'sum'],
          metaValueDefault: 'sum',
        });
        columns.push({
          headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.BOOKINGS_ACTION',
          measureKey: 'actions_bookings',
          metaValueList: ['mean', 'median', 'sum'],
          metaValueDefault: 'sum',
        });
        columns.push({
          headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.CONVERSATIONS_ACTION',
          measureKey: 'actions_conversations',
          metaValueList: ['mean', 'median', 'sum'],
          metaValueDefault: 'sum',
        });
        columns.push({
          headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.FOOD_ORDERS_ACTION',
          measureKey: 'actions_food_orders',
          metaValueList: ['mean', 'median', 'sum'],
          metaValueDefault: 'sum',
        });

        return columns;
      }),
    );
  }

  ngOnDestroy(): void {
    this.connectedCardsService.forceMobile(false);
    this.sidepanelService.clearView();
    this.sidepanelService.close();
  }

  onMeasureSelected(measureKey: string): void {
    this.measureSelected.set(measureKey);
  }
}

// Distributes a statistic value evenly over selected dates.
function distributeStatOverSelectedDates(statsByTime: StatByTime<InsightsData>[], stat: number): any {
  const result = [];
  const noOfDaysSelected = statsByTime.length;
  const statPerDay = Math.round(stat / noOfDaysSelected);
  for (const item of statsByTime) {
    const startDate = new Date(item.localAlignedTime);
    result.push({
      localAlignedTime: startDate,
      stat: statPerDay,
    });
  }

  return result;
}
