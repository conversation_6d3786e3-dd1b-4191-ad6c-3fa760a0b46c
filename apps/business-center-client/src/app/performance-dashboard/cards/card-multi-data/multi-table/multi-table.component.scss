@use 'design-tokens' as *;
@use '../../../../core/breaks.scss' as breaks;

$POSITIVE_CHANGE: #4caf50;
$NEGATIVE_CHANGE: #c42727;
$SECONDARY_COLOR: #7d7d7d;

.table-container {
  padding: 16px;
  padding-top: 0;
  text-align: left;
  @include breaks.respond-to(mobile) {
    overflow: scroll;
  }

  .table {
    width: 100%;
    border-collapse: collapse;

    .title-row {
      height: 44px;
      .heading-col {
        max-width: 250px;
        @include breaks.respond-to(mobile) {
          min-width: 125px;
          padding: 0px 8px 0px 8px;
        }
      }
      .data-col {
        font-size: 10px;
        color: #7e7e7e;
        @include breaks.respond-to(mobile) {
          min-width: 50px;
          padding: 0px 8px 0px 8px;
        }
      }
      .data-col.has-grade {
        font-size: 14px;
        color: $secondary-font-color;
        font-weight: 400;
      }
    }
    .data-row {
      padding: 4px;

      .heading-col {
        font-weight: 500;
        max-width: 250px;
        display: flex;
        align-items: center;
        margin: 2px;
        overflow-wrap: anywhere;

        img {
          height: 24px;
          width: 24px;
          margin: 8px;
        }
        a,
        div {
          width: 100%;
          vertical-align: middle;
          padding-left: 8px;
          @include breaks.respond-to(mobile) {
            min-width: 125px;
            padding: 0px 8px 0px 8px;
          }
        }
      }
      .heading-col.has-grade {
        height: 64px;
        font-weight: 400;
      }

      .data-col {
        font-size: 12px;
        @include breaks.respond-to(mobile) {
          min-width: 100px;
          padding: 0px 8px 0px 8px;
        }

        &.has-grade {
          font-size: 14px;
        }

        .col-description {
          display: none;
        }
        span {
          display: inline-block;

          .current {
            margin-right: 4px;

            mat-icon {
              width: 20px;
              height: 20px;
              font-size: 16px;
              line-height: inherit;
              text-align: center;
              vertical-align: top;
              margin-right: 4px;
            }
            .value {
              min-width: 25px;
              text-align: right;
            }
          }
          .value {
            vertical-align: top;
          }
          .value,
          .previous {
            display: inline-block;
          }
          .previous {
            margin-top: 0;
            padding-left: 4px;
            font-size: 12px;
            line-height: 15px;
            height: 12px;

            .trend {
              vertical-align: top;
              display: inline-block;
              width: 20px;

              ::ng-deep mat-icon {
                height: 12px;
                font-size: 16px;
              }
            }
            .value {
              display: inline-block;
              font-weight: normal;
              font-size: 12px;
              line-height: 15px;
              height: 12px;
            }
          }
          .previous:not(.positive-change):not(.negative-change) {
            color: #a1a1a1;
            font-size: 10px;
            text-transform: lowercase;
            font-weight: bold;
            line-height: 15px;
          }

          .previous.positive-change {
            color: $POSITIVE_CHANGE;
          }
          .previous.negative-change {
            color: $NEGATIVE_CHANGE;
          }
        }
      }
    }
    .data-row.has-icon {
      line-height: 30px;
    }
    .data-row.has-grade {
      height: 64px;
    }
    .data-row:nth-of-type(even) {
      background: $secondary-background-color;
    }
  }

  button {
    display: block;
    margin: 8px auto 0 auto;
  }
}

.no-data-container {
  text-align: center;
  display: table;
  min-height: 200px;
  height: 100%;
  width: 100%;

  .no-data {
    display: table-cell;
    vertical-align: middle;
    color: $secondary-text-color;

    h3 {
      margin: 8px 0 0 0;
    }
  }
}

mat-card-content {
  &.loading-content {
    .stencil-shimmer {
      height: 90%;
      width: 95%;
      margin: 2%;
      min-height: 215px;
    }
  }
}

.table-thermometer {
  width: 100px;
  padding: 8px;

  :only-child {
    border-radius: 0.25rem;
  }

  :first-child {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }

  :last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
  }
}

.table-thermometer-bar {
  height: 0.5rem;
  display: inline-block;
}

.has-tool-tip {
  text-decoration: underline dotted;
  text-decoration-color: #a1a1a1;
}

.table-title {
  font-size: 14px;
  font-weight: 400;
  color: $secondary-font-color;
}

.table-grade {
  border-radius: 50%;
  height: 32px;
  width: 32px;
  margin-left: 2px;
  text-align: center;
  color: white;
  font-size: 14px;
  line-height: 32px;
  font-weight: 500;
}
