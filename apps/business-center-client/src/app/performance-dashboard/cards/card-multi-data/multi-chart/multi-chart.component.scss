@use 'design-tokens' as *;

:host {
  display: flex;
  flex-flow: column;
  height: 100%;
}

$POSITIVE_CHANGE: #4caf50;
$NEGATIVE_CHANGE: #c42727;

.chart-container {
  position: relative;
  width: 100%;
  min-height: 200px;
}
.chart-container.no-limit-height {
  max-height: inherit;
}
.chart-container.margin-bottom {
  margin-bottom: 8px;
}
.legend {
  vertical-align: center;
  padding-left: 4px;

  &.position-bottom {
    padding-bottom: 8px;
  }
  .legend-option {
    display: flex;
    flex-flow: row;
    font-size: 12px;
    padding-right: 16px;

    .color-container {
      flex: 0 0 40px;

      .color {
        height: 16px;
        width: 16px;
        margin: 4px auto 0 auto;
        border-radius: 4px;
        overflow: hidden;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }
    }
    .name-container {
      flex: 1;
      line-height: 14px;
      padding-top: 6px;
    }
    .value-container {
      display: flex;
      padding: 6px 8px 0 16px;
      height: 12px;
      line-height: 12px;
      padding-top: 6px;

      .current-value {
        flex: 1;
        text-align: right;
      }
      .current-value,
      .value-change {
        display: inline-block;
        vertical-align: top;
      }
      .value-change {
        line-height: 12px;
        vertical-align: top;
        height: 12px;
        margin: 0;
        padding-left: 4px;
        font-size: 12px;

        .trend {
          vertical-align: bottom;
          display: inline-block;
          width: 20px;

          ::ng-deep mat-icon {
            font-size: 20px;
            line-height: 12px;
            height: 12px;
            vertical-align: top;
          }
        }
        .value {
          display: inline-block;
          font-weight: normal;
          font-size: 12px;
          line-height: 12px;
          vertical-align: top;
          min-width: 16px;
        }
      }
      .value-change:not(.positive-change):not(.negative-change) {
        color: #a1a1a1;
        font-size: 10px;
        text-transform: lowercase;
        font-weight: bold;
        vertical-align: top;
        line-height: 12px;
        margin-left: 4px;
      }

      .value-change.positive-change {
        color: $POSITIVE_CHANGE;
      }
      .value-change.negative-change {
        color: $NEGATIVE_CHANGE;
      }
    }
  }
  .legend-option:not(:first-child) {
    padding-top: 4px;
  }
}

.no-data-container {
  text-align: center;
  display: table;
  min-height: 200px;
  height: 100%;
  width: 100%;

  .no-data {
    display: table-cell;
    vertical-align: middle;
    color: $secondary-text-color;

    h3 {
      margin: 8px 0 0 0;
    }
  }
}

.detailed-breakdown-toggle {
  text-align: center;
  padding: $spacing-2 $spacing-2 $spacing-1;
  margin-top: $spacing-2;
  cursor: pointer;

  mat-icon,
  span {
    vertical-align: top;
    height: $spacing-3;
    line-height: $spacing-3;
    color: $secondary-text-color;
  }
}
