@use 'design-tokens' as *;

$breakpoint-xs: 600px;
$breakpoint-sm: 768px;

.form-step {
  padding-top: 16px;
}

.product-overview {
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 24px;

  h1 {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
  }
}

.product-id {
  display: flex;
  width: 100%;
  flex-grow: 1;

  va-icon {
    margin-right: 20px;

    @media screen and (max-width: $breakpoint-xs) {
      :host ::ng-deep .va-icon-container {
        width: 40px !important;
        height: 40px !important;
      }
    }
  }

  .product-id-text {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  span {
    display: block;
    overflow: hidden;
  }

  .tagline {
    color: $secondary-text-color;
    margin-top: 8px;
    font-size: 16px;
  }

  @media screen and (min-width: $breakpoint-xs) {
    width: 66%;
    padding-right: 16px;
  }
}

input {
  font-family: Roboto, 'Helvetica Neue', Arial, Helvetica, sans-serif;
  font-size: 15px;
  float: left;
  width: 80%;
  height: 37px;
  padding: 5px 5px 5px 15px;
}

.domain-search {
  padding: 35px 10px 10px;
  margin: auto;
  width: 100%;
  max-width: 400px;
}

.domain-search-result {
  padding: 0px 10px 10px;
  margin: auto;
  display: flex;
  flex-direction: column;
  min-height: 70px;
}

.search-button {
  width: 40px;
  min-width: 40px;
  padding: 0;
}

.icon-and-message {
  display: flex;
  align-items: center;
  p {
    padding-left: 13px;
    padding-top: 1px;
  }
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  height: 100px;
}

.suggested-domains {
  font-size: 16px;
  display: flex;
  min-width: 350px;
  height: 100px;
}

.suggested-domains-inner-wrap {
  display: flex;
  width: 100%;
}

.suggested-domains-text {
  font-size: 18px;
  padding-bottom: 5px;
}

.center-self {
  margin: auto;
}

.suggested-loading-icon {
  width: 40px;
  height: 40px;
}

.suggestion {
  margin-top: 10px;
  margin-left: 10px;
}

.info {
  padding-left: 8px;
  background-color: $light-blue;
  margin-right: 0.4em;
  font-size: 13px;
  mat-icon {
    color: $glxy-blue-600;
  }
}

.search-result-message {
  display: flex;
  justify-content: center;
  width: 95%;
}

.success {
  color: $green;
  font-size: 18px;
}

.fail {
  color: $red;
  font-size: 18px;
}

.domain-error-help-text {
  color: $dark-gray;
  font-size: 16px;
  margin-top: 0;
}

mat-card {
  margin-bottom: 16px;
}

.button-container {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
}

.divider {
  margin-bottom: 10px;
}

.terms-of-service-section {
  padding-top: 10px;
}

.terms-of-service-checkbox {
  margin-right: 3px;
}

.domain-loading-container {
  padding: 0px 10px 10px;
  margin: auto;
  max-width: 400px;
  min-height: 70px;
  display: flex;
  align-items: center;
}

.domain-search-loading {
  display: inline-block;
  margin-right: 5px;
}

.domain-search-loading-text {
  display: inline-block;
  vertical-align: super;
  padding-left: 8px;
  font-size: 18px;
}

.terms-of-service-link {
  cursor: pointer;
  color: $blue;
}
