@use 'design-tokens' as *;

.container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 12px;
  border-radius: 2px;
  margin-bottom: 10px;

  .message {
    display: flex;
    align-items: center;
    color: $secondary-text-color;

    span {
      margin-left: 12px;
    }
  }

  .action {
    display: flex;
    align-items: center;
  }
}
app-status-banner:last-child {
  margin-bottom: 0px;
}

.warning {
  background-color: #fff8e3;
  .icon {
    color: #f4ae00;
  }
}
.border {
  border: 1px solid #ffc107; /* Switch to use $yellow from uikit */
}
.info {
  background-color: #dff0fd;
  border: 1px solid #90caf9; /* mat blue 200 */

  .icon {
    color: $blue;
  }
}
.error {
  background-color: $light-red;
  border: 1px solid $red; /* mat blue 200 */

  .icon {
    color: $red;
  }
}
