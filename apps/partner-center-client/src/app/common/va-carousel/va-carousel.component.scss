@use 'design-tokens' as *;

* {
  box-sizing: border-box;
}

a:active {
  font-family: Roboto, 'Helvetica Neue', sans-serif;
}
a:hover {
  text-decoration: none;
}
a[mat-button],
a[mat-button]:visited {
  color: #fff;
}

button {
  height: auto;
}

.carousel-container {
  max-width: 100%;
  display: flex;
  flex-wrap: nowrap;
  overflow: hidden;
  margin: auto;
  position: relative;
}

.carousel-slide-container {
  display: block;
  min-width: 100%;
  padding: 20px 60px;
  flex-direction: column;
  font-size: 14px;
  color: #fff;
  background-position: center;
  background-size: cover;
  aspect-ratio: 1.435;

  &.active-slide {
    order: -1;
  }
}

.circle-icon {
  background: #9e9e9e;
  border-radius: 50% !important;
  font-size: 34px;
  padding: 2px;
}

.carousel-slide-container-v2 {
  display: block;
  min-width: 100%;
  padding: 0px;
  flex-direction: column;
  font-size: 14px;
  color: #fff;
  background-position: center;
  background-size: cover;
  text-align: center;

  &.active-slide {
    order: -1;
  }
}

.slide-title {
  font-size: 18px;
  color: #fff;
}

.slide-desc {
  width: 60%;
}

.slide-title-v2 {
  font-size: 16px;
  color: $secondary-text-color;
  text-align: center;
}

.slide-desc-v2 {
  width: 100%;
  text-align: center;
  padding-bottom: 20px;
}

.slide-markers {
  position: absolute;
  bottom: 0;
  width: 100%;
  display: flex;

  .slide-marker {
    height: 4px;
    flex: 1;
    background: #fff;
    opacity: 0.3;
  }

  .slide-marker-active {
    opacity: 1;
  }
}

.prev,
.next {
  cursor: pointer;
  position: absolute;
  top: 50%;
  width: auto;
  margin-top: -7px;
  color: white;
  font-weight: bold;
  font-size: 24px;
  transition: 0.6s ease;
  border-radius: 0 3px 3px 0;
  user-select: none;
  opacity: 78%;
  margin: 0 4px 0;
}

.next {
  right: 0;
  border-radius: 3px 0 0 3px;
}

.prev:hover,
.next:hover {
  background-color: rgba(0, 0, 0, 0.8);
}
