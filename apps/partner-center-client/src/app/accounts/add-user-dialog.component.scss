@use 'design-tokens' as *;

.mat-mdc-dialog-actions {
  display: flex;
}

.tertiary-button {
  justify-content: space-between;
}

.mat-mdc-dialog-title {
  margin: 0;
  padding: 0 16px 16px;
}

:host ::ng-deep .va-item-content-container {
  overflow: hidden;
}

:host ::ng-deep app-list-item .va-item-title {
  color: $primary-text-color !important;
}

:host ::ng-deep app-list-item .va-item-subtitle {
  color: $secondary-text-color !important;
  overflow: hidden;
  text-overflow: ellipsis;
}

:host ::ng-deep va-filter-container .va-item-container {
  padding: 0 8px;
}

:host ::ng-deep va-filter-container .toolbar {
  z-index: 1;
  position: sticky;
  top: 0;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
}

.truncate-email {
  display: block;
  width: 250px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 10px;
}

.create-form-container {
  padding: 16px;
}

.content {
  flex: auto;
  overflow: auto;
  padding: 0;
  width: 100%;
}

mat-dialog-content {
  height: 65vh;
  display: flex;
  flex-direction: column;
  padding: 0;
}
