import { inject, InjectionToken } from '@angular/core';
import {
  AvailableFields,
  FormBuilderDependencies,
  FormPreview,
  MappedFieldService,
  UnmappedFieldService,
} from '@galaxy/form-builder';
import { FormConfigFieldInterface, FormsApiService, JsonSchemaLibrary } from '@vendasta/forms_microservice';
import { Observable, of, switchMap } from 'rxjs';
import { catchError, distinctUntilChanged, filter, map, shareReplay } from 'rxjs/operators';
import { AppConfigService } from '../app-config.service';
import { FeatureFlagService } from '@galaxy/partner';

export const FormBuilderInjectionToken = new InjectionToken<FormBuilderDependencies>(
  'dependencies given to form builder library',
  {
    factory: () => {
      const formService: FormsApiService = inject(FormsApiService);
      const appConfigService = inject(AppConfigService);
      const unmappedFieldService: UnmappedFieldService = inject(UnmappedFieldService);
      const mappedFieldService: MappedFieldService = inject(MappedFieldService);
      const featureFlagService = inject(FeatureFlagService);

      const partnerId$: Observable<string> = appConfigService.config$.pipe(
        map((config) => config.partnerId),
        filter((partnerId) => !!partnerId),
        distinctUntilChanged(),
        shareReplay(1),
      );
      mappedFieldService.namespace = partnerId$;
      return {
        namespace$: partnerId$,
        schemaLibrary: JsonSchemaLibrary.JSON_SCHEMA_LIBRARY_JSONFORM,
        routePrefix$: of('/custom-forms'),
        parentNamespace$: of(''),
        hasSections$: partnerId$.pipe(
          switchMap((namespace) => {
            return featureFlagService.batchGetStatus(namespace, '', ['custom_form_section']);
          }),
          map((featureFlagStatus) => featureFlagStatus['custom_form_section'] ?? false),
          shareReplay({ refCount: true, bufferSize: 1 }),
        ),

        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        listFields: (search: string, cursor: string, pageSize: number): Observable<AvailableFields[]> => {
          const genericFields = unmappedFieldService.getUnmappedFields(search);

          return mappedFieldService.buildMappedFields(search).pipe(
            map((results) => {
              return [
                genericFields.find((group) => group.labelKey === 'CUSTOM_FORMS.LIST_OF_FIELDS.ELEMENT'),
                ...results,
                genericFields.find((group) => group.labelKey === 'CUSTOM_FORMS.LIST_OF_FIELDS.GENERIC'),
              ];
            }),
          );
        },
        previewForm(formFields: FormConfigFieldInterface[], submitButtonLabel?: string): Observable<FormPreview> {
          return formService
            .previewForm({
              formConfig: {
                fields: formFields,
                submitButtonLabel: submitButtonLabel || '',
              },
              library: this.schemaLibrary,
            })
            .pipe(
              catchError((err) => {
                console.error('error fetching preview for form', err);
                return of({ jsonSchema: '{}', jsonUiSchema: '[]' });
              }),
            );
        },
      } as FormBuilderDependencies;
    },
  },
);
