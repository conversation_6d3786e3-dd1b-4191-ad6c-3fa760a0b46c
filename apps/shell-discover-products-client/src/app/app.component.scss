@use 'design-tokens' as *;

:host {
  display: block;
  padding-top: 40px; // Atlas Nav Bar height
}

// Simulate Atlas Nav Bar
.top-nav-bar {
  background-color: $contrast-background-color;
  color: white;
  height: 40px;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  margin: 0;
  padding: 0;
  width: 100%;
  z-index: 1000;
}

// Sidenav
.sidenav-header {
  text-align: center;
  padding: $spacing-4;
}
.glxy-nav-footer {
  padding: $spacing-4;
  text-align: center;
}
img.top-logo {
  width: 200px;
  height: auto;
}
.sidenav-header-text {
  margin-top: 8px;
  font-size: 14px;
  color: $tertiary-font-color;
}
.footer-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
