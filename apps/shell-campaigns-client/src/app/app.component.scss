@use 'design-tokens' as *;

:host {
  display: block;
  padding-top: 40px; // simulate Atlas Nav Bar
}

// Simulate Atlas Nav Bar
.top-nav-bar {
  background-color: $contrast-background-color; // atlas nav color
  color: white;
  height: 40px;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  margin: 0;
  padding: 0;
  width: 100%;
  z-index: 1000;
}

// Sidenav header
.sidenav-header {
  text-align: center;
  padding: $spacing-4;
}

img.top-logo {
  width: 200px;
  height: auto;
}
.sidenav-header-text {
  margin-top: 8px;
  font-size: 14px;
  color: $tertiary-font-color;
}

.glxy-nav-footer {
}

.padding {
  padding: $spacing-4;

  &.no-top {
    padding-top: 0;
  }
  &.no-bottom {
    padding-bottom: 0;
  }
}
