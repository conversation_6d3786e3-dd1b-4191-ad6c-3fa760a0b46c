@use 'design-tokens' as *;

:host {
  display: block;
  padding-top: 40px; // Atlas Nav Bar height
}

// Simulate Atlas Nav Bar
.top-nav-bar {
  background-color: $contrast-background-color;
  color: white;
  height: 40px;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  margin: 0;
  padding: 0;
  width: 100%;
  z-index: 1000;
}

// Sidenav
.sidenav-header {
  text-align: center;
  padding: $spacing-4;
}

.glxy-nav-footer {
  padding: $spacing-4;
  text-align: center;
}

img.top-logo {
  width: 200px;
  height: auto;
}

.sidenav-header-text {
  margin-top: 8px;
  font-size: 14px;
  color: $tertiary-font-color;
}

.footer-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

// Atlas

.atlas-navbar-space {
  display: flex;
  align-items: center;
  height: 100%;
  flex-grow: 1;
  justify-content: space-between;
  min-width: 0;
}

.atlas-title-container {
  flex-grow: 1;
  height: 100%;
}

.atlas-title {
  height: 100%;
  min-width: 0;
  display: None;
}

.atlas-header {
  font-size: 14px;
  font-weight: 400;
  height: 100%;
  display: flex;
  align-items: center;
}

.atlas-item__icon {
  margin: 0 10px;
}

.atlas-item__text {
  margin: 0 10px 0 17px;
  overflow: hidden;
  white-space: nowrap;
}

.atlas-spacer {
  margin: 0 auto;
}

.atlas-actions {
  display: flex;
  height: 100%;
}
