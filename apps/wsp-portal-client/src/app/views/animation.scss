//animation scene setup
@use 'sass:math';

@use 'design-tokens' as *;

.canvas {
  background: #f5f5f5;
  overflow: hidden;
  margin: -24px;

  div {
    font-size: 0;
    position: absolute;
  }

  &.canvas-hide {
    display: none;
  }
}

.viewport,
.outro-left,
.outro-right,
.star-layer {
  width: 100%;
  height: 100%;
}

.viewport {
  background: #e1f5fe;
  position: absolute;
  transform-origin: 50%;
  transform: translateY(42px) scale(2);
}

.texts {
  width: 100%;
  bottom: 25%;
  text-align: center;
  color: #fff;
  opacity: 0.8;

  .mat-progress-bar-fill::after {
    background-color: $blue !important;
  }

  .mat-progress-bar-buffer {
    background: #b3e5fc !important;
  }

  .creation-progress {
    width: 50%;
    margin: auto;
  }

  .text {
    width: 500px;
    padding: 10px 20px;
    left: 50%;
    margin-left: -250px;
    border-radius: 40px;
    font-size: 20px;
    white-space: nowrap;
    color: #00796b;
    background: #e1f5fe;
    opacity: 0;

    span {
      display: block;
      font-size: 14px;
      color: #999;
    }
  }
}

.svg-cloud {
  fill: #fff;
}

.cloud-background .svg-cloud {
  fill: #eceff1;
}

.cloud {
  top: 50%;
  left: 50%;
  margin-left: -197px;
  margin-top: -120px;
  width: 350px;
  height: 194px;

  .svg-cloud {
    width: 100%;
  }
}

.inner-cloud {
  .cloud-foreground,
  .cloud-background {
    width: 100%;
    height: 100%;
  }
}

.inner-cloud-left {
  width: 122px;
  height: 70px;
  left: 50px;
  top: 100px;

  .cloud-background {
    width: 114px;
    left: 12px;
    top: -4px;
  }
}

.inner-cloud-right {
  width: 110px;
  height: 62px;
  left: 225px;
  top: 100px;

  .cloud-background {
    width: 102px;
    left: -8px;
    top: 2px;
  }
}

.cloud2 {
  width: 150px;
  height: 80px;
  top: 25%;
  left: 55%;

  .inner-cloud {
    transform: rotateY(180deg);
    transform-origin: 50%;
    left: 0;
    width: 100%;

    .svg-cloud {
      fill: $primary-background-color;
    }
  }
}

.house {
  width: 134px;
  height: 156px;
  padding-top: 20px;
  top: -20px;
  left: 130px;
  overflow: hidden;

  .house-svg {
    position: absolute;
    width: 134px;
    height: 124px;
    transform: translateY(110%);
    left: 0;
  }
}

.shelf {
  width: 18px;
  height: 0;
  overflow: hidden;
  bottom: 31px;
  left: 94px;
}

.fence {
  width: 100px;
  height: 0;
  overflow: hidden;
  bottom: 0;
  left: 10px;
}

.sign {
  width: 18px;
  height: 8px;
  left: 38px;
  top: 90px;

  .sign-part {
    opacity: 0;
  }
}

.person {
  width: 30px;
  left: 34px;
  top: 104px;
  opacity: 0;

  svg {
    width: 30px;
    height: 48px;
  }

  .arm {
    transform-origin: 10px 23px;
    transform: rotate(20deg);
  }
}

.shelf-items {
  width: 12px;
  height: 29px;
  left: 98px;
  top: 89px;

  .shelf-item {
    opacity: 0;
  }
}

//animation for creating a site

$animation-length: 30; //seconds
$scenes: 6;
$scene-length: math.div($animation-length, $scenes);
//ordering - change scene start order by changing the multiplier
$house-scene: 2;
$shelf-scene: $scene-length * 1;
$fence-scene: $scene-length * 2;
$sign-scene: $scene-length * 3;
$items-scene: $scene-length * 4;
$person-scene: $scene-length * 5;

.animate {
  display: block;

  .text {
    animation: appear 0.3s ease-out;
    animation-fill-mode: forwards;
  }

  .text1 {
    opacity: 1;
  }

  .text2 {
    animation-delay: #{$shelf-scene}s;
  }

  .text3 {
    animation-delay: #{$fence-scene}s;
  }

  .text4 {
    animation-delay: #{$sign-scene}s;
  }

  .text5 {
    animation-delay: #{$items-scene}s;
  }

  .text6 {
    animation-delay: #{$person-scene}s;
  }

  .text7 {
    animation: none;
  }

  .cloud {
    animation: bob 2s ease-in-out 0s infinite alternate;
  }

  .cloud2 {
    animation: bob 2s ease-in-out 1s infinite alternate;
  }

  .house {
    .house-svg {
      animation: rise 1.3s ease-in-out #{$house-scene}s 1 forwards;
    }
  }

  .shelf {
    animation: reveal-shelf 0.3s ease-in-out #{$shelf-scene}s forwards;
  }

  .fence {
    animation: reveal-shelf 0.3s ease-in-out #{$fence-scene}s forwards;
  }

  .sign {
    .sign-part {
      animation: appear 0s ease-out;
      animation-fill-mode: forwards;
    }

    .frame {
      animation-duration: 0.3s;
      animation-delay: #{$sign-scene}s;
    }

    .h {
      animation-delay: #{$sign-scene + 0.3}s;
    }

    .o {
      animation-delay: #{$sign-scene + (0.3 * 2)}s;
    }

    .m {
      animation-delay: #{$sign-scene + (0.3 * 3)}s;
    }

    .e {
      animation-delay: #{$sign-scene + (0.3 * 4)}s;
    }
  }

  .shelf-items {
    .shelf-item {
      animation: appear 0s ease-out;
      animation-fill-mode: forwards;
    }

    .one {
      animation-delay: #{$items-scene}s;
    }

    .two {
      animation-delay: #{$items-scene + 0.3}s;
    }

    .three {
      animation-delay: #{$items-scene + (+0.3 * 2)}s;
    }

    .four {
      animation-delay: #{$items-scene + (+0.3 * 3)}s;
    }
  }

  .person {
    animation: appear 0.3s ease-out #{$person-scene}s forwards;

    .arm {
      animation: wave 0.2s ease-in-out #{$person-scene + 0.3}s 4 alternate;
    }
  }
}

//animation for restoring a site
//timing is also controlled in the keyframes as a % of total play time
$restore-animation-length: 10; //seconds
$waiting-scene-start: 5;
$sunrise-scene-start: 8;

//additional scene setup
.star {
  top: 30%;
  left: 30%;
  width: 10px;
  height: 10px;
  background: #e1f5fe;
  border-radius: 50%;
  transform-origin: 50%;
  opacity: 0.6;

  &.star2 {
    top: 40%;
    left: 65%;
    width: 6px;
    height: 6px;
  }

  &.star3 {
    top: 15%;
    left: 56%;
    width: 8px;
    height: 8px;
  }

  &.shooting-star {
    width: 60px;
    height: 6px;
    top: 5%;
    left: 65%;
    opacity: 0;
    transform: rotate(-30deg) translateX(100px);
  }
}

.animate-restore {
  display: block;

  .star {
    animation: twinkle 2s ease-in-out 0s infinite alternate;

    &.star2 {
      animation-delay: 0.4s;
    }

    &.star3 {
      animation-delay: 1s;
    }

    &.shooting-star {
      animation: shoot 6s ease-in-out 0s infinite forwards;
    }
  }

  .viewport {
    transform: scale(1);
    animation: darken-sky #{$restore-animation-length}s ease-in-out 1s 1 forwards;
  }

  .svg-cloud {
    animation: darken-cloud #{$restore-animation-length}s ease-in-out 1s 1 forwards;
  }

  .cloud {
    animation: bob 2s ease-in-out 0s infinite alternate;

    .cloud-background .svg-cloud {
      fill: #f6f6f6;
      animation: none;
    }
  }

  .cloud2 {
    animation: bob 2s ease-in-out 1s infinite alternate;

    .svg-cloud {
      fill: #fff;
    }
  }

  .house {
    .wall-door {
      animation: darken-house1 #{$restore-animation-length}s ease-in-out 1s 1 forwards;
    }

    .wall {
      animation: darken-house2 #{$restore-animation-length}s ease-in-out 1s 1 forwards;
    }

    .door,
    .window {
      animation: darken-house3 #{$restore-animation-length}s ease-in-out 1s 1 forwards;
    }

    .roof,
    .roof-door,
    .roof-window {
      animation: darken-house4 #{$restore-animation-length}s ease-in-out 1s 1 forwards;
    }

    .house-svg {
      transform: translateY(0);
      animation: fall-rise #{$restore-animation-length}s ease-in-out 3s 1 forwards;
    }
  }

  .text {
    animation: appear 0.3s ease-out;
    animation-fill-mode: forwards;

    span {
      animation: appear 0.6s ease-in-out 0s infinite alternate;
    }
  }

  .text1 {
    opacity: 1;
  }

  .text2 {
    animation-delay: #{$waiting-scene-start}s;
  }

  .text3 {
    animation-delay: #{$sunrise-scene-start}s;
  }

  .text-end {
    animation: none;
  }
}

//animation for clearing the stage after either create or restore animations

.outro {
  .text {
    animation: none;
    opacity: 0;
  }

  .text7,
  .text-end {
    animation: appear 0.3s ease-out 0s 1 forwards;
  }

  .outro-left {
    animation: outro-left 1s ease-in-out 1s 1 forwards;
  }

  .outro-right {
    animation: outro-right 1s ease-in-out 1s 1 forwards;
  }

  .texts {
    animation: appear 1s ease-in-out 1s 1 reverse forwards;
  }
}

//keyframes used in the above animations

@keyframes bob {
  0% {
    transform: translateY(0%);
  }
  100% {
    transform: translateY(-10%);
  }
}

@keyframes rise {
  0% {
    transform: translateY(110%);
  }
  90% {
    transform: translateY(-5%);
  }
  100% {
    transform: translateY(0%);
  }
}

@keyframes reveal-shelf {
  0% {
    height: 0;
  }
  100% {
    height: 30px;
  }
}

@keyframes wave {
  0% {
    transform: rotate(20deg);
  }
  100% {
    transform: rotate(-20deg);
  }
}

@keyframes appear {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

//restore:

@keyframes darken-cloud {
  0% {
    fill: #fff;
  }
  20% {
    fill: #ccc;
  }
  80% {
    fill: #ccc;
  }
  100% {
    fill: #fff;
  }
}

@keyframes darken-sky {
  0% {
    background: #e1f5fe;
  }
  20% {
    background: #311b92;
  }
  80% {
    background: #311b92;
  }
  100% {
    background: #e1f5fe;
  }
}

@keyframes darken-house1 {
  0% {
    fill: #d1c4e9;
  }
  20% {
    fill: #b2b2b2;
  }
}

@keyframes darken-house2 {
  0% {
    fill: #b39ddb;
  }
  20% {
    fill: #999999;
  }
}

@keyframes darken-house3 {
  0% {
    fill: #9575cd;
  }
  20% {
    fill: #7f7f7f;
  }
}

@keyframes darken-house4 {
  0% {
    fill: #6a1b9a;
  }
  20% {
    fill: #4c4c4c;
  }
}

@keyframes fall-rise {
  0% {
    transform: translateY(0);
  }
  2% {
    transform: translateY(-5%);
  }
  10% {
    transform: translateY(110%);
  }
  90% {
    transform: translateY(110%);
  }
  98% {
    transform: translateY(-5%);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes shoot {
  0% {
    opacity: 0;
    width: 60px;
    transform: rotate(-30deg) translateX(100px);
  }
  1% {
    opacity: 1;
    width: 160px;
  }
  15% {
    opacity: 0;
    width: 6px;
    transform: rotate(-30deg) translateX(-900px);
  }
}

@keyframes twinkle {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  10% {
    opacity: 0.6;
    transform: scale(0.6);
  }
}

// outro:

@keyframes outro-left {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(-20%);
    opacity: 0;
  }
}

@keyframes outro-right {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(20%);
    opacity: 0;
  }
}
