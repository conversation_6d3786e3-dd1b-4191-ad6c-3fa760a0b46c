@use 'design-tokens' as *;

// Remove padding and sizing restrictions
::ng-deep .mat-dialog-no-padding {
  max-width: 99vw !important;
}

::ng-deep .mat-dialog-no-padding .mat-mdc-dialog-container {
  padding: 0p;
}

::ng-deep .mat-dialog-no-padding .mat-mdc-dialog-content {
  max-height: 99vh;
  margin: 0;
  padding: 0;
}

#email-preview-iframe {
  transition: width 0.5s;
}

// Main Dialog
.wrapper {
  display: flex;
  height: 90vh;
  width: 90vw;
  min-width: 960px;
  color: $primary-font-color;
}

.email-preview-pane {
  flex: 1 1 650px;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}

.email-info-pane {
  display: flex;
  flex: 0 0 320px;
  width: 320px;
  flex-direction: column;
  position: relative;
}

.close-modal {
  position: absolute;
  right: 0px;
}

// Info
.email-info-container {
  padding: 40px 32px 12px;
  border-bottom: solid 1px rgba(0, 0, 0, 0.12);
}

.subject,
.email-to-from {
  padding-bottom: 16px;
}

.email-name {
  font-size: 14px;
}
.email-email {
  font-size: 14px;
  color: $secondary-font-color;
}

.info-label {
  display: block;
  font-size: 12px;
  color: $gray;
}
.subject .info-label {
  display: block;
}

mat-tab-group {
  height: 100%;
  overflow-y: auto;
}

.event-list {
  padding: 0 14px;

  mat-icon {
    color: $secondary-font-color;
  }
  h4.mat-line {
    font-size: 15px;
    color: $secondary-font-color;
  }
  p.mat-line {
    font-size: 13px;
    color: #858585;
  }
}

.attribute-list {
  list-style-type: none;
  padding: 20px 32px;
  margin: 0;

  li {
    padding-bottom: 16px;
  }
  .key {
    font-size: 13px;
    padding-bottom: 4px;
    color: $secondary-font-color;
    font-weight: 500;
  }
  .value {
    font-family: 'Roboto Mono', monospace;
    font-weight: 400;
    background: $lighter-gray;
    word-break: break-all;
    display: inline-block;
    border-radius: 4px;
    font-size: 13px;
    padding: 4px 8px;
    color: $primary-font-color;
  }
}

// Email Preview Pane
.email-preview-controls {
  background: white;
  text-align: center;
  padding: 8px 0;
  border-bottom: 1px solid #d8d8d8;

  display: flex;
  justify-content: center;
  flex: 0 0 36px;

  button {
    color: #5f6268;
  }
  mat-icon {
    font-size: 20px;
    margin-top: 1px;
  }

  .code-previews {
    padding-left: 36px;
  }
}

.email-preview {
  background: grey;
  position: relative;
  flex: 1 1;
}

.email-preview #source-code {
  display: none;
  box-sizing: border-box;
  font-family: 'Roboto Mono', monospace;
  background: $contrast-background-color;
  color: white;
  font-size: 12px;
  width: 100%;
  height: 100%;
  padding: 24px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.email-preview iframe {
  width: 100%;
  height: 100%;
  margin: 0 auto;
  border: 0;
  display: block;
  background: white;
}

.email-preview iframe::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.26);
}
