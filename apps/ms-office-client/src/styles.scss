/* You can add global styles to this file, and also import other style files */
@use '@angular/material' as mat;
@use 'design-tokens' as *;

@import 'uikit';
@import 'uikit_theme';

// Include the common styles for Angular Material. We include this here so that you only
// have to load a single css file for Angular Material in your app.
// **Be sure that you only ever include this mixin once!**

@include mat.all-component-typographies();
@include mat.elevation-classes();
@include mat.app-background();

@include va-uikit-global();
@include va-utilities();
@include va-modal();
@include va-stencil();

// Define the default theme
$app-primary: mat.m2-define-palette(mat.$m2-blue-palette, 600, 400, 800);
$app-accent: mat.m2-define-palette(mat.$m2-green-palette);
$app-warn: mat.m2-define-palette(mat.$m2-red-palette, 800);
$app-theme: mat.m2-define-light-theme($app-primary, $app-accent, $app-warn);
@include mat.all-component-themes($app-theme);

body {
  font-family: 'Roboto', 'Helvetica Neue', Arial, Helvetica, sans-serif;
  line-height: 1.4;
  font-size: 14px;
  margin: 0;
  padding: 0;
  background-color: $primary-background-color;
  -webkit-font-smoothing: antialiased;
}

.filters h2 {
  margin: 0;
}

a {
  color: mat.m2-get-color-from-palette($app-primary);
  text-decoration: none;
  cursor: pointer;

  &:visited {
    color: mat.m2-get-color-from-palette($app-primary);
  }

  &:hover {
    color: mat.m2-get-color-from-palette($app-primary, darker);
  }
}

.sidebar-section {
  // header, footer, account-id
  padding: $gutter-width-dense;
}

.content {
  padding-bottom: 80px;
  margin: 0 auto;
  width: 100%;
}

.icon-and-message {
  display: flex;
  align-items: center;
  mat-icon {
    margin-right: 10px;
  }
}

.button-spinner {
  display: flex;
  justify-content: center;
}
