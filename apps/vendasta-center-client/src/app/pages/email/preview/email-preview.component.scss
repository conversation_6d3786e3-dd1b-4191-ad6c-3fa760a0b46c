@use 'design-tokens' as *;

// Remove padding and sizing restrictions

::ng-deep .mat-dialog-no-padding {
  max-width: 99vw !important;
}

::ng-deep .mat-dialog-no-padding .mat-mdc-dialog-container {
  padding: 0px;
}

::ng-deep .mat-dialog-no-padding .mat-mdc-dialog-content {
  max-height: 99vh;
  margin: 0;
  padding: 0;
}

#email-preview-iframe {
  transition: width 0.5s;
}

// Main Dialog
.wrapper {
  display: flex;
  height: 90vh;
  width: 90vw;
  min-width: 960px;
  color: $primary-font-color;
}
.email-preview-pane {
  flex: 1 1 650px;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
.email-info-pane {
  border-left: 1px solid #d8d8d8;
  display: flex;
  flex: 0 0 340px;
  width: 340px;
  flex-direction: column;
  position: relative;
}

.close-modal {
  position: absolute;
  top: 4px;
  right: 8px;
}

// Info
.email-info-container {
  padding: 40px 32px 24px;
  border-bottom: solid 1px rgba(0, 0, 0, 0.12);
}

.subject,
.email-to-from {
  padding-bottom: 16px;
}

.email-name {
  font-size: 14px;
}
.email-email {
  font-size: 14px;
  color: $secondary-font-color;
}

.info-label {
  display: block;
  font-size: 12px;
  color: $gray;
}
.subject .info-label {
  display: block;
}

mat-tab-group {
  height: 100%;
  overflow-y: auto;
}

.event-list {
  mat-icon {
    color: $secondary-font-color;
  }
  h4 {
    font-size: 15px;
    color: $secondary-font-color;
    margin: 0;
  }
  p.line {
    font-size: 13px;
    color: #858585;
    margin: 0;
  }
  .event-row {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    box-sizing: border-box;
    justify-content: flex-start;
  }
  .event-details {
    display: flex;
    flex-direction: column;
    margin-left: 8px;
  }
}

.event-list,
.info-list {
  list-style-type: none;
  padding: 16px 24px;
  margin: 0;

  li {
    padding-bottom: 16px;
  }
}

.info-value {
  font-family: $monospace-font-family;
  color: $primary-font-color;
  background: $lighter-gray;
  font-size: 13px;
  font-weight: 400;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  border-radius: 4px;
  padding: 4px 8px;
  overflow-wrap: anywhere;
}

.key {
  font-size: 13px;
  padding-bottom: 4px;
  color: $secondary-font-color;
  font-weight: 500;
}

.copy-icon {
  color: $gray;
  margin-left: 8px;
  cursor: pointer;
  vertical-align: middle;
  opacity: 50%;
  &:hover {
    opacity: 100%;
  }
}

// Email Preview Pane
.email-preview-controls {
  background: white;
  text-align: center;
  padding: 8px 0;
  border-bottom: 1px solid #d8d8d8;

  display: flex;
  justify-content: center;
  flex: 0 0 36px;
  flex-flow: row nowrap;

  .layouts {
    flex-grow: 1;
    & > * {
      display: inline-block;
    }

    button {
      color: #5f6268;
    }

    mat-icon {
      font-size: 20px;
      margin-top: 1px;
    }

    .code-previews {
      padding-left: 36px;
    }
  }
}

.share-button {
  margin: 0 $spacing-2;
  button {
    color: white;
  }
}

.email-preview {
  background: grey;
  position: relative;
  flex: 1 1;
}

.spam-tab {
  display: flex;
}
.spam-failed {
  color: red;
}
.spam-passed {
  color: green;
}
.spam-text {
  margin: 0 $spacing-2;
  align-self: center;
}

.email-preview #source-code {
  display: none;
  box-sizing: border-box;
  font-family: $monospace-font-family;
  background: $contrast-background-color;
  color: white;
  font-size: 12px;
  width: 100%;
  height: 100%;
  padding: 24px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.email-preview iframe {
  width: 100%;
  height: 100%;
  margin: 0 auto;
  border: 0;
  display: block;
  background: white;
}

.email-preview iframe::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.26);
}

.dev-buttons {
  display: flex;
  gap: $spacing-2;
  padding: $spacing-2;
  width: 100%;

  a,
  & > button {
    width: 50%;
  }

  a > button {
    width: 100%;
  }
}
