@use 'design-tokens' as *;
@import '../../../styles.scss';

.preferred-sites-page {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: $gutter-width;
}

.content-heading {
  margin-top: $spacing-2;
  @include text-preset-3--bold;
}

.handle {
  cursor: move;
  cursor: grab;
}

.material-icons.handle:active {
  cursor: grabbing;
}

.source-info-container {
  display: flex;
  flex-direction: row;
  align-items: center;

  .source-name-container {
    width: 20%;
    min-width: 64px;

    .source-name {
      @include text-preset-4;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }

  .listing-message {
    @include text-preset-5;
    color: $secondary-text-color;
  }

  .open-in-new {
    margin: 0 $spacing-2;
    color: $link-color;
    text-decoration: none;
  }

  .source-url-field {
    width: 100%;
    margin-right: $spacing-2;
  }
}

.mdc-list-item.mdc-list-item--with-one-line {
  height: 72px;
}

.hidden {
  visibility: hidden;
}

.send-to-google-container {
  padding-top: 5px;
  display: flex;
}

.send-to-google-words-container {
  margin-left: 10px;
}

.form-tooltip {
  color: $primary-color;
  font-size: 10px;
  width: 100px;
}

.description-text {
  color: $secondary-font-color;
}

.faded-description-text {
  color: $gray;
  margin-top: 10px;
  font-weight: 300;
}

.legal-source-use-text {
  color: $gray;
  margin-top: 10px;
  font-weight: 300;
  padding: $spacing-4;
  font-size: 12px;
}

.checkbox-words {
  font-weight: 500;
}

.cdk-drag-placeholder {
  opacity: 0;
}

.upgrade-cta {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 0;
  background-color: $primary-background-color;

  .cta-title {
    font-size: 16px;
    font-weight: bold;
    padding-top: 5px;
    padding-bottom: 10px;
  }

  .cta-content {
    font-size: 14px;
    text-align: center;
    width: 60%;
    color: #616161;
  }
}
