@use '@angular/material' as mat;

@import 'galaxy-base';

// Plus imports for other components in your app.
@import 'uikit';
@import 'uikit_theme';

@include va-stencil();

@include va-modal();

@include va-uikit-global();
@include va-utilities();

$media-query-size: 900px;

h1 {
  font-weight: lighter;
}

$cv-margin: 20px;

mat-form-field {
  width: 100%;
}

app-page {
  width: 75%;
  margin: $cv-margin auto $cv-margin auto;

  .sub-header {
    margin: $cv-margin 0 0 $cv-margin;
    color: $secondary-font-color;
  }

  mat-horizontal-stepper {
    background-color: $primary-background-color !important;
  }

  .mat-step-header .mat-step-icon {
    background-color: $primary-color;
  }

  .mat-horizontal-stepper-header {
    pointer-events: none !important;
  }

  .mat-horizontal-stepper-header-container {
    width: 60%;
    margin: auto;
  }

  .workflow-button {
    margin: $cv-margin;
  }

  .customize-template-button {
    float: right;
  }

  .two-columns-card {
    width: 100%;
    flex-basis: 45%;
    flex-grow: 1;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: $gutter-width;
  }

  .card-container {
    gap: $gutter-width;
    display: flex;
    flex-wrap: wrap;
  }

  @media (max-width: $media-query-size) {
    .mat-horizontal-stepper-header-container {
      width: 100%;
    }
  }
}

.breadcrumbs {
  margin: $cv-margin;
  font-size: 12px;

  a {
    color: $secondary-font-color;
    text-decoration: none;
  }

  span {
    color: $primary-font-color;
  }
}

@keyframes stencil {
  0% {
    background-position-x: 0%;
  }
  100% {
    background-position-x: 100%;
  }
}

.stencil {
  background: linear-gradient(to right, #{$light-gray} 40%, #{$gray} 50%, #{$light-gray} 60%);
  background-size: 1200%;
  animation: stencil 2s linear 0s infinite reverse;
}

.cdk-overlay-container .mat-mdc-menu-panel {
  max-width: 370px;
}

.container-descriptive-text {
  color: $dark-gray;
  width: 100%;
}

.stats-card-container {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  width: 101%;
}

.stats-card {
  margin: 10px;
  min-width: 150px;
  width: 200px;
  height: 130px;
  flex-shrink: 0.5;
}

.stats-card-title {
  font-size: 14px;
  color: $secondary-font-color;
}

.stats-card-main-text {
  font-size: 36px;
}

.daterange-selector {
  width: 100px;
  font-size: 14px;
  float: right;
  margin-top: -30px;
  margin-right: -50px;
  height: 50px;
}

.mat-select-value {
  color: $secondary-font-color;
}

.customize-template-button {
  float: right;
}

.page-link {
  text-decoration: none;
  margin-bottom: 50px;
  color: $primary-color;
  font-weight: 400;
}

.dialogClass {
  mat-dialog-container {
    max-height: 90vh;
    overflow: scroll;
  }
}

@media (max-width: $media-query-size) {
  .daterange-selector {
    padding-top: 35px;
    float: left;
  }

  .stats-card-container {
    width: 65%;
    justify-content: center;
  }
}

::ng-deep .filter-button {
  z-index: 0 !important;
}

::ng-deep #side-nav-container mat-sidenav-content {
  mat-toolbar {
    z-index: 2 !important;
  }

  .mobile-action-bar {
    z-index: 2;
  }
}

#timerange-container {
  padding: 0;
  min-height: 500px;
}

.cv-upgrade-dialog {
  .mat-mdc-dialog-container {
    padding: 0;
  }
}

// make the page-toolbar sticky
// so the `.glxy-menu-toggle` can
// stay on it when scrolling
.glxy-page.page-is-mobile {
  height: 100% !important;

  .glxy-page-toolbar {
    position: sticky;
    // --top-gap is declared by glxy-nav
    top: var(--top-gap);
    background: $primary-background-color;
  }

  .glxy-page-main-content {
    overflow: auto !important;
    height: 100% !important;
  }
}
