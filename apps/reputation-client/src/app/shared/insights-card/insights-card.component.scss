@use 'design-tokens' as *;

.grid-list {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.insight-text {
  color: $dark-gray;
  padding-bottom: $spacing-2;
}

.insight-chip-container {
  width: 100%;
}

.insight-chip {
  padding-top: 16px;
  cursor: pointer;

  .insight-positive {
    font-weight: normal;
    background: $light-green;
    color: $green;

    .count {
      padding-left: 4px;
      color: $dark-gray;
      font-weight: 500;
    }
  }

  .insight-negative {
    font-weight: normal;
    background: $light-red;
    color: $red;

    .count {
      padding-left: 4px;
      color: $dark-gray;
      font-weight: 500;
    }
  }

  .insight-neutral {
    font-weight: normal;
    background: $light-gray;
    color: $dark-gray;

    .count {
      padding-left: 4px;
      color: $dark-gray;
      font-weight: 500;
    }
  }
}

mat-card-content {
  height: 100%;

  &.loading-content {
    height: 100%;
    width: 100%;
    margin-bottom: 8px;

    .stencil-shimmer {
      height: 100%;
    }
  }
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 16px;
  width: 100%;
  height: 100%;

  ::ng-deep {
    .empty-state--description {
      font-size: 14px;
      color: $secondary-text-color;
    }

    .empty-state--link {
      font-size: 14px;
    }
  }
}
