@use 'design-tokens' as *;
@import 'hide-sizes';

rm-chart {
  height: 300px;
}

.stars {
  display: flex;
  flex: 0 0 20%;
  align-self: center;
  justify-content: flex-end;
  white-space: nowrap;
}

.star {
  color: #bdbdbd;
  height: 16px;
  width: 16px;
  font-size: 16px;
}

.star-lit {
  color: #f7cb16;
  height: 16px;
  width: 16px;
  font-size: 16px;
}

.divider {
  width: 100%;
}

.source-icon {
  border-radius: 50%;
}

.list {
  width: 100%;

  .review-site-item {
    display: flex;
    flex: 1 1 auto;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;

    .review-site-icon {
      display: flex;
      flex: 0 0 30%;
      align-items: center;
      justify-content: flex-start;
      gap: $spacing-3;
    }

    .review-bar-container {
      display: flex;
      flex: 0 0 40%;
      align-items: center;
      justify-content: space-between;
      gap: $spacing-3;

      @media screen and (max-width: 960px) {
        flex: 0 0 30%;
      }
    }
  }
}

span {
  white-space: nowrap;
}

.count {
  font-size: 12px;
}

.force-hover-state {
  .ngx-charts {
    height: 140px;
  }

  ::ng-deep .line-highlight {
    display: inline !important;
  }
}

.total-value {
  font-size: 28px;
  padding-top: 15px;

  .total-value-hours {
    font-size: 12px;
  }
}

.review-sources {
  flex-grow: 1;
}

.average-response-time {
  margin-left: $gutter-width;
  max-width: 375px;
}

.chart-title {
  padding-left: 10px;
  padding-top: 10px;
  margin-bottom: 0;
  font-style: italic;
}

.double-graphs {
  display: flex;
  justify-content: space-between;
}

.bottom-border {
  border-bottom: 1px solid $border-color;
}

.no-data {
  padding-top: 10px;
  font-size: 16px;
  color: #bdbdbd;
}

.no-reviews-responded-to {
  padding: 10px 16px;
}

.sub-header {
  font-size: 12px;
  color: $secondary-text-color;
}

::ng-deep .va-card {
  padding-bottom: 12px;
}

@media (max-width: 1500px) {
  .double-graphs {
    flex-direction: column;
  }

  .average-response-time {
    margin-top: 20px;
    margin-left: 0;
    max-width: none;
  }
}
