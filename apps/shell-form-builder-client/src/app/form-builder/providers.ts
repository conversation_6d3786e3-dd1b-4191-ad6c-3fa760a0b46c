import { inject, InjectionToken } from '@angular/core';
import { map, Observable, of, switchMap } from 'rxjs';
import {
  AvailableFields,
  FormBuilderDependencies,
  FormPreview,
  MappedFieldService,
  UnmappedFieldService,
} from '@galaxy/form-builder';
import { FormConfigFieldInterface, FormsApiService, JsonSchemaLibrary } from '@vendasta/forms_microservice';
import { catchError, distinctUntilChanged, filter, shareReplay } from 'rxjs/operators';
import { PartnerIdService } from '../partner-id.service';
import { FeatureFlagService } from '@galaxy/partner';

export const FormBuilderInjectionToken = new InjectionToken<FormBuilderDependencies>(
  'dependencies given to form builder library',
  {
    factory: () => {
      const formService: FormsApiService = inject(FormsApiService);
      const partnerIdService: PartnerIdService = inject(PartnerIdService);
      const unmappedFieldService: UnmappedFieldService = inject(UnmappedFieldService);
      const mappedFieldService: MappedFieldService = inject(MappedFieldService);
      const partnerId$: Observable<string> = partnerIdService.partnerId$.pipe(
        filter((partnerId) => !!partnerId),
        distinctUntilChanged(),
        shareReplay(1),
      );
      const featureFlagService = inject(FeatureFlagService);

      return {
        namespace$: partnerId$,
        schemaLibrary: JsonSchemaLibrary.JSON_SCHEMA_LIBRARY_JSONFORM,
        routePrefix$: of('/custom-forms'),
        parentNamespace$: of(''),
        hasSections$: partnerId$.pipe(
          switchMap((namespace) => {
            return featureFlagService.batchGetStatus(namespace, '', ['custom_form_section']);
          }),
          map((featureFlagStatus) => featureFlagStatus['custom_form_section'] ?? false),
          shareReplay({ refCount: true, bufferSize: 1 }),
        ),

        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        listFields: (search: string, cursor: string, pageSize: number): Observable<AvailableFields[]> => {
          const genericFields = unmappedFieldService.getUnmappedFields(search);

          mappedFieldService.namespace = partnerId$;

          return mappedFieldService.buildMappedFields(search).pipe(
            map((results) => {
              return [
                genericFields.find((group) => group.labelKey === 'CUSTOM_FORMS.LIST_OF_FIELDS.ELEMENT'),
                ...results,
                genericFields.find((group) => group.labelKey === 'CUSTOM_FORMS.LIST_OF_FIELDS.GENERIC'),
              ];
            }),
          );
        },
        previewForm(formFields: FormConfigFieldInterface[], submitButtonLabel?: string): Observable<FormPreview> {
          return formService
            .previewForm({
              formConfig: {
                fields: formFields,
                submitButtonLabel: submitButtonLabel || '',
              },
              library: this.schemaLibrary,
            })
            .pipe(
              catchError((err) => {
                console.error('error fetching preview for form', err);
                return of({ jsonSchema: '{}', jsonUiSchema: '[]' });
              }),
            );
        },
      } as FormBuilderDependencies;
    },
  },
);
