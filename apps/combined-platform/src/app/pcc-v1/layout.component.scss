@use 'design-tokens' as *;

$max-width-desktop: 768px;
$max-width-mobile: 425px;

$mobile-header-height: 60px + 1px;

:host {
  display: flex;
  flex-direction: column;
  height: 100vh;

  @media screen and (max-width: $max-width-desktop) {
    display: block;
    height: unset;
    min-height: 100vh;
  }
}

.header-wrapper {
  flex-grow: 0;
  flex-shrink: 0;
  width: 100%;
  border-bottom: 1px solid $border-color;
  background-color: white;
  // box-shadow: 0 -2px 10px 2px #00000022;
  z-index: 2;

  @media screen and (max-width: $max-width-desktop) {
    position: sticky;
    top: 0;
  }
}

.app-content {
  display: flex;
  flex-grow: 1;
  flex-shrink: 1;
  height: 100%;
  position: relative;
  overflow: hidden;

  &.nav-is-mobile {
    overflow: unset;
    position: unset;
    height: unset;
    min-height: calc(100vh - $mobile-header-height);
  }
}

.main-router-content {
  flex-grow: 1;
  flex-shrink: 1;

  @media screen and (max-width: $max-width-desktop) {
    display: block;
    overflow: unset;
  }
}

.main-content {
  flex-grow: 1;
  flex-shrink: 1;
}

.sidenav {
  flex-grow: 0;
  flex-shrink: 0;
  background-color: $primary-background-color;
  border-right: 1px solid $border-color;
  width: 276px;
  z-index: 100;
}
.sidenav-router-content {
  padding: 32px 24px;
  display: block;
  opacity: 0;
  transform: translateY(-8px);

  &.show {
    animation: 0.2s ease-out forwards slideDownAndFade;
    @keyframes slideDownAndFade {
      0% {
        transform: translateY(-8px);
        opacity: 0;
      }
      100% {
        transform: translateY(-0);
        opacity: 1;
      }
    }
  }
}

.sidepanel {
  flex-grow: 0;
  flex-shrink: 0;
  background-color: $white;
  border-left: 1px solid $border-color;
  width: 276px;

  @media screen and (max-width: $max-width-mobile) {
    width: 100%;
  }
}

// --------

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  padding: 0 24px;
  padding-right: 10px;
}

.header-logo-wrapper {
  display: flex;
  align-items: center;
}

.header-logo-button {
  padding: 8px 10px;
}

.nav-toggle {
  margin-right: 4px;
  margin-left: -6px;
  line-height: 0;
}

.site-search {
  width: 100%;
  max-width: 400px;
  position: relative;
  margin-left: 24px;
  margin-right: 10px;

  .mat-icon {
    position: absolute;
    top: 17px;
    left: 8px;
    font-size: 20px;
    line-height: 0;
    user-select: none;
    color: $tertiary-font-color;
  }
  input {
    border: 1px solid #e0e0e0;
    padding: 8px 15px;
    padding-left: 32px;
    border-radius: 4px;
    font-size: 14px;
    width: 100%;
    background-color: $primary-background-color;
  }
}

.user-and-settings {
  display: flex;
  align-items: center;
  color: #6c727f;
  // margin-right: 16px;
  gap: 4px;

  .mat-mdc-icon-button {
    line-height: 0;
  }
}
.profile-divider {
  height: 32px;
  border-left: 1px solid $border-color;
  margin-left: 4px;
  margin-right: -2px;
}
.profile-button {
  height: auto;
  padding: 6px 12px;
}
.profile-button-wrapper {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  gap: 10px;
}
.profile-button-logo {
  width: auto;
  height: 30px;
}

// ------

.nav-wrapper {
  height: 49px;
  width: 100%;
  overflow: hidden;
  transition:
    opacity 0.5s cubic-bezier(0.25, 0.8, 0.25, 1),
    height 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);

  &.nav-collapsed {
    opacity: 0;
    height: 0;
  }
}

.user-menu-account-info {
  padding: 12px 16px;
  // padding-bottom: 10px;
}

.mobile-user-settings {
  display: none;
}

.show-on-mobile {
  display: none;
}

@media screen and (max-width: 560px) {
  .profile-button,
  .profile-divider {
    display: none;
  }
  .mobile-user-settings {
    display: block;
  }
}

@media screen and (max-width: 425px) {
  .hide-on-mobile {
    display: none;
  }
  .show-on-mobile {
    display: unset;
  }
}

///////////

::ng-deep {
  .glxy-page-toolbar {
    background-color: $lightest-grey;
  }

  .mat-drawer-backdrop.mat-drawer-shown {
    background-color: rgba(0, 0, 0, 0.075);
    position: fixed;
  }
}

/////////////

.mobile-nav-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.mobile-nav-header {
  flex-grow: 0;
  flex-shrink: 0;
}

.mobile-nav-divider {
  border-color: $border-color;
  border-style: none;
  border-bottom-style: solid;
  height: 0px;
  margin: 8px;
}

.mobile-nav-animation-wrapper {
  flex-grow: 3;
  flex-shrink: 3;
  position: relative;
  overflow-x: clip;
}

.mobile-nav-footer {
  flex-grow: 0;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  padding: 16px;
}

.mobile-app-logo {
  margin: 16px auto;
  margin-top: 32px;
  width: fit-content;
}

.mobile-nav-parents,
.mobile-nav-children {
  padding: 16px 24px;
}
.mobile-nav-parents {
  transform: translateX(-40px);
}
.mobile-nav-children {
  transform: translateX(40px);
}

.mobile-nav-animate {
  position: absolute;
  top: 0px;
  width: 100%;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  pointer-events: none;

  &.show {
    visibility: visible;
    transform: translateX(0);
    opacity: 1;
    pointer-events: all;
  }
}

.mobile-nav-add-left-spacing {
  padding-left: 24px;
}

.mobile-nav-children-back {
  position: relative;
  top: 1px;
  left: -2px;
}

.flexwrap {
  display: flex;
  justify-content: space-between;
}
.mobile-active-arrow {
  opacity: 0.4;
}
app-shared-nav-item:hover .mobile-active-arrow {
  opacity: 1;
}
app-shared-nav-item ::ng-deep a.active .mobile-active-arrow {
  opacity: 1;
}

.mobile-nav-select {
  padding: 0 24px;
  padding-top: 24px;
  margin-bottom: -8px;
}
