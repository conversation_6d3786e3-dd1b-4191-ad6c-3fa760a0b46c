@use 'design-tokens' as *;

.projects-due-next-container {
  border-radius: 6px;
  background-color: white;
  border: solid 1px #e0e0e0;
  height: 100%;
  .title {
    font-size: $font-preset-3-size;
    padding: $spacing-3;
  }
  .project-row-container {
    padding: $spacing-3;
    font-size: $font-preset-4-size;
    .project-row {
      display: flex;
      justify-content: space-between;
      .project-icon {
        margin-right: $spacing-2;
      }
      .icon-title {
        flex: 1 1 45%;
        display: flex;
        align-items: center;
        .title-wrapper {
          .project-title {
            font-size: $font-preset-4-size;
            margin-right: 2px;
            font-weight: 500;
          }
        }
      }
      .assignee {
        flex: 0 0 27.5%;
        width: 100px;
        padding: 0 $spacing-2 0 $spacing-2;
      }
      .due-date {
        flex: 0 0 27.5%;
        color: $blue;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        .due-date-wrapper {
          width: 130px;
        }
        mat-chip-list {
          margin-top: 4px;
        }
        .due-in-text {
          color: $tertiary-font-color;
          font-size: $font-preset-5-size;
          font-style: italic;
          padding-top: 4px;
        }
      }
    }
  }
  .header-title {
    display: flex;
    justify-content: space-between;
    .all-container {
      padding: $spacing-3;
      display: flex;
      align-items: center;
      .view-all-projects {
        font-size: $font-preset-5-size;
        cursor: pointer;
        text-decoration: none;
      }
      mat-icon {
        color: $tertiary-font-color;
        vertical-align: middle;
        margin-right: $spacing-2;
      }
    }
  }
  .grey-background {
    background-color: $primary-background-color;
  }
}

.empty-state {
  padding: $spacing-3;
  font-size: $font-preset-4-size;
}

:host ::ng-deep {
  .red-chip {
    background: $light-red !important;
    border: 1px solid $dark-red;
    span {
      color: $dark-red !important;
    }
  }
  .yellow-chip {
    background: $light-yellow !important;
    border: 1px solid $dark-yellow;
    span {
      color: $dark-yellow !important;
    }
  }
  .green-chip {
    background: $light-green !important;
    border: 1px solid $dark-green;
    span {
      color: $dark-green !important;
    }
  }
}

:host .mat-mdc-standard-chip {
  min-height: $spacing-4;
  font-weight: normal;
  font-style: italic;
  font-size: $font-preset-5-size;
  width: max-content;
}

.loading-shimmer {
  height: 64px;
  margin: $spacing-2;
  width: 98%;
}
