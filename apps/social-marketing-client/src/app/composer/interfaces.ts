import { SocialService, WorkflowType } from './post';
import { Customization } from '../core/post/customization';
import { Location, PostType } from '@vendasta/social-posts';
import { PostInterface } from '@vendasta/composer';
import { PostableService } from '../shared/social-service/social-service.service';
import { MLPosts } from '../shared/dynamic-posts/dynamic-posts-models';

export interface Coupon {
  url: string;
  name: string;
}

export interface MediaLimits {
  source?: string;
  message?: string;
  values?: string;
}

export interface SiteMetaContent {
  caption: string;
  description: string;
  images: string[];
  link: string;
  name: string;
}

export class UploadedFile {
  url: string;
  fileType: FileType;
  path?: string;
  name?: string;
  size?: number;
  lastPostedDate?: Date;
  isDraft?: boolean;
  errors?: unknown;
  naturalWidth?: number;
  naturalHeight?: number;
  duration?: number;
  videoHeight?: number;
  videoWidth?: number;

  constructor(data: any) {
    this.url = data.url;
    this.fileType = data.fileType;
    this.path = data.path;
    this.name = data.name;
    this.size = data.size;
    this.naturalHeight = data?.naturalHeight ?? undefined;
    this.naturalWidth = data?.naturalWidth ?? undefined;
    this.duration = data?.duration ?? undefined;
    this.videoHeight = data?.videoHeight ?? undefined;
    this.videoWidth = data?.videoWidth ?? undefined;

    if (data.lastPostedDate) {
      this.lastPostedDate = new Date(data.lastPostedDate.getTime());
    }
    this.isDraft = data.isDraft;
  }
}

export enum FileType {
  VIDEO = 'video',
  IMAGE = 'image',
  GIF = 'gif',
}

export interface LinkHistory {
  lastPostedDate: Date;
  isDraft: boolean;
}

export interface ReviewDetails {
  sharedReviewId?: string;
  sourceId?: number;
  domain?: string;
  content?: string;
  stars?: number;
}

export type CustomizationSetup = {
  services: Map<PostableService, string>;
  customizations: Customization[];
};

export interface ComposerSettings {
  groupedCustomization?: Customization;
  draftId?: string;
  templateId?: string;
  templateTitle?: string;
  date?: Date;
  content?: string;
  composerLink?: string;
  composerHint?: string;
  review?: ReviewDetails;
  requiredContent?: RequiredContent[];
  isEditing?: boolean;
  hasGif?: boolean;
  // brandId is for editing a multilocationPost in single location SM
  brandId?: string;
  // multilocationId is for editing multilocationPost in brands composer
  multilocationId?: string;
  // activeLocations when editing an ML post
  activeLocations?: Location[];
  isBundleAIPost?: boolean;
  isHiddenDraft?: boolean;
  composerCallback?: (post: PostInterface[] | MLPosts, mode: 'single' | 'customize') => void;
  customizationSetup?: CustomizationSetup;
  workFlowType?: WorkflowType;
  triggeredFrom?: string;
  //TO DO: blogData?: any has to be replaced with blogData?: BlogData interface in future.
  blogData?: any;
  postType?: PostType;
  isCSVUploadedPost?: boolean;
  selectedMLlocations?: typeServiceMlSelected;
  filter?: Map<string, string[]>;
}

export interface RequiredContent {
  content?: string;
  correctionMessage?: string;
}

export interface VFormSection {
  label: string;
  name: string;
  options: { label: string; value: string }[];
}

export interface InterestingContentItem {
  feed_item_type: 'rss' | 'mention';
  service: string;

  // Post mention
  imageUrl?: string;
  name?: string;
  permalink?: string;
  postContextText?: string;
  postCreatedDateTime?: string;
  postId?: string;
  postText?: string;
  postType?: string;
  postableServices?: {
    profileImageUrl: string;
    serviceName: string;
    ssid: string;
  }[];
  postedByOwner?: boolean;
  profileImageUrl?: string;
  profileUrl?: string;
  replyToPostId?: string;
  socialSearchIds?: string;
  ssid?: string;
  tags?: string[];
  username?: string;

  // RSS feed item
  author?: string;
  categories?: string[];
  created?: string;
  feed_name?: string;
  parent_rss_url?: string;
  posted_date?: string;
  summary?: string;
  taxonomy_id?: string;
  title?: string;
  updated?: string;
  url?: string;
}

export interface LimitingSource {
  size: number;
  limitingSource: SocialService;
}

export interface ImageAssociation {
  image_id: string;
  created: Date;
  deleted: Date;
}

export interface VideoAssociation {
  videoId: string;
  videoUrl: string;
}

export interface UploadVideoAssociation {
  mediaLink: string;
}

export enum PanelType {
  MEDIA = 'media',
  LINK = 'link',
  CONTENT = 'content',
  EMOJI = 'emoji',
  COUPON = 'coupon',
  PREVIEW = 'preview',
  CROP = 'crop',
  TEMPLATES = 'templates',
  DYNAMIC_CONTENT = 'dynamic_content',
  SUGGESTIONS = 'suggestions',
}

export interface MetaData {
  propertyName: string;
  propertyValue: string;
}

export interface Media {
  image_url?: string;
  image_path?: string;
  image_size?: string;
  video_url?: string;
  meta_data?: MetaData[];
}

// This interface is used when calling SM's task manager handler. In the case of most
// social posts the handler is really only concerned with storing the linkUrl and image
// hashes so we can warn the user when they're using repeated images/old links. But in the
// cases where a TaskId or ParentTaskId is present on the SocialPostId, the handler defers
// a process to add a task/subtask in ARM(task manager). This interface insures the data coming
// back from the composer µservice is turned into a json object that has the named values
// SM and ARM are both expecting.
export interface TaskManagerPost {
  ssid: string; // Both routes in TM
  username: string; // Both routes in TM (!important: This is the name of the service)
  postText: string; // All (SM/TM)
  imageUrl: string; // Both routes in TM (First image in list)
  imageUrls: string[]; // Need all for the image hashes in SM
  scheduledDateTime: Date; // TM expects this naming/SM needs value
  linkUrl: string; // SM
  socialPostId: string; // SM
}

export interface SubmitResults {
  failures?: {
    serviceId: string;
    reason: string;
  }[];
}

export interface SocialMentionInterface {
  id: string;
  name: string;
  screenName: string;
  formattedScreenName: string;
  profileImageUrl: string;
  serviceType: string;
  serviceIcon: string;
  defaultProfileImageUrl: string;
}

export enum MentionPromptOptions {
  NONE = -1,
  CTA_TWITTER = 0,
  CTA_FACEBOOK = 1,
  EMPTY_RESULT = 2,
  SELECT_SERVICE = 3,
  CONNECT_SERVICE = 4,
}

export interface SocialMentionResult {
  result: SocialMentionInterface[];
  promptCode?: number;
}

export interface typeServiceMlSelected {
  facebook: boolean;
  gmb: boolean;
  instagram: boolean;
  linkedin: boolean;
}
