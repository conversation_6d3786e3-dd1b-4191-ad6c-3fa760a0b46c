<div
  class="v-composer"
  *ngIf="visible"
  [style.height]="'calc(100% - ' + topNavBarOffset + 'px)'"
  [style.top]="topNavBarOffset + 'px'"
>
  @if (!composerLoaded) {
    <glxy-loading-spinner [fullHeight]="true" [size]="'large'"></glxy-loading-spinner>
  } @else {
    <mat-card appearance="outlined" class="composer">
      <mat-sidenav-container
        [hasBackdrop]="(selectedPanel$ | async) !== panelType.SUGGESTIONS && mediaTabGroupSelectedIndex !== 2"
      >
        <mat-sidenav-content>
          <mat-toolbar *ngIf="displayToolbar">
            <button [ngStyle]="{ color: '#1e88e5' }" mat-icon-button *ngIf="showClose" (click)="closeComposer(true)">
              <mat-icon>arrow_back</mat-icon>
            </button>
            {{ getWorkFlowTypeText() | translate }}

            <span class="spacer"></span>

            <button
              mat-button
              *ngIf="
                (hasTwoStageFlag$ | async) === false &&
                composerStore?.triggeredFrom === TriggeredFrom.CALENDAR.toString() &&
                isUWM &&
                (isMobile$ | async) === true
              "
              class="toggle-previews"
              (click)="openPreviewPanel()"
            >
              {{ 'COMPOSER.VIEW_PREVIEW' | translate }}
            </button>
          </mat-toolbar>

          @if (isUWM) {
            <mat-card-content
              class="composer-content"
              [hidden]="
                !((!postLoading && (displayTwoStage$ | async) === false && (isMobile$ | async) === false) || isUWM)
              "
            >
              <div class="composer-wrapper">
                <ng-container *ngTemplateOutlet="composePage"> </ng-container>
                <ng-container *ngTemplateOutlet="previewPage"> </ng-container>
              </div>
            </mat-card-content>
          } @else {
            <mat-card-content
              class="composer-content"
              *ngIf="!postLoading && (displayTwoStage$ | async) === false && (isMobile$ | async) === false"
            >
              <div class="composer-wrapper">
                <ng-container *ngTemplateOutlet="composePage"> </ng-container>
                <ng-container *ngTemplateOutlet="previewPage"> </ng-container>
              </div>
            </mat-card-content>

            <div class="mat-tab-group-container">
              <mat-tab-group
                mat-stretch-tabs="false"
                mat-align-tabs="start"
                animationDuration="0ms"
                *ngIf="(displayTwoStage$ | async) || (isMobile$ | async)"
              >
                <mat-tab label="Create">
                  <ng-container *ngTemplateOutlet="composePage"> </ng-container>
                </mat-tab>
                <mat-tab label="Preview">
                  <ng-container *ngTemplateOutlet="previewPage"> </ng-container>
                </mat-tab>
              </mat-tab-group>
            </div>
          }
          <div *ngIf="showFooter">
            <ng-container *ngTemplateOutlet="composerFooter"> </ng-container>
          </div>
        </mat-sidenav-content>
        <mat-sidenav (openedChange)="sidePanelOpened($event)" #sidePanel class="side-panel" mode="over" position="end">
          <div class="side-panel-wrapper">
            <glxy-page
              (closedStart)="onSidePanelClose()"
              [pagePadding]="(selectedPanel$ | async) !== panelType.SUGGESTIONS"
            >
              <glxy-page-toolbar class="sm-ai-toolbar">
                <glxy-page-title>
                  {{ sidePanelTitle$ | async | translate }}
                  <span class="beta-spacer">
                    <glxy-badge *ngIf="(selectedPanel$ | async) === panelType.SUGGESTIONS" [color]="'blue'">
                      {{ 'COMPOSER.CREATE_IMAGE.BETA' | translate }}
                    </glxy-badge>
                  </span>
                </glxy-page-title>

                <glxy-page-actions>
                  <a class="close-panel" (click)="sidePanel.close()">
                    <mat-icon>close</mat-icon>
                  </a>
                </glxy-page-actions>
              </glxy-page-toolbar>

              <mat-tab-group
                mat-stretch-tabs="false"
                mat-align-tabs="start"
                animationDuration="0ms"
                *ngIf="(selectedPanel$ | async) === panelType.MEDIA"
                [(selectedIndex)]="mediaTabGroupSelectedIndex"
              >
                <mat-tab label="First">
                  <ng-template mat-tab-label>
                    <span class="composer-media-tabs">
                      {{ 'COMPOSER.UPLOAD_FILES' | translate }}
                    </span>
                  </ng-template>
                  <app-upload-files [saveToAccount]="false" (mediaSelected)="sidePanel.close()"></app-upload-files>
                </mat-tab>
                <mat-tab *ngIf="mediaLibraryEnabled$ | async" label="Second">
                  <ng-template mat-tab-label>
                    <span class="composer-media-tabs">
                      {{ 'COMPOSER.MEDIA_LIBRARY' | translate }}
                    </span>
                  </ng-template>

                  <app-media-library></app-media-library>
                </mat-tab>
                <mat-tab *ngIf="featuresService.unsplashImageEnabled$ | async" label="Third">
                  <ng-template mat-tab-label>
                    <span class="composer-media-tabs">
                      {{ 'COMPOSER.UNSPLASH_IMAGES' | translate }}
                    </span>
                  </ng-template>

                  <app-unsplash-library></app-unsplash-library>
                </mat-tab>
                <mat-tab label="Fourth">
                  <ng-template mat-tab-label>
                    <span class="composer-media-tabs">
                      {{ 'COMPOSER.PIXABAY_IMAGES' | translate }}
                    </span>
                  </ng-template>

                  <app-pixabay-library></app-pixabay-library>
                </mat-tab>
                <mat-tab label="Fifth">
                  <ng-template mat-tab-label>
                    <span class="composer-media-tabs">
                      {{ 'COMPOSER.CREATE_IMAGE.CONTENT_LABEL' | translate }}
                    </span>
                    <glxy-badge [color]="'blue'">{{ 'COMPOSER.CREATE_IMAGE.BETA' | translate }}</glxy-badge>
                  </ng-template>
                  <app-create-image-ai></app-create-image-ai>
                </mat-tab>
                <mat-tab label="Sixth">
                  <ng-template mat-tab-label>
                    <span class="composer-media-tabs">
                      {{ 'COMPOSER.GIFS_BY_TENOR' | translate }}
                    </span>
                  </ng-template>
                  <app-tenor-gifs (imageSelected)="sidePanel.close()"></app-tenor-gifs>
                </mat-tab>
              </mat-tab-group>
              <app-v-composer-image-cropper
                #imageCropper
                [hidden]="(selectedPanel$ | async) !== panelType.CROP"
                (croppedImageSelected)="croppedImageSelected($event)"
              ></app-v-composer-image-cropper>
              <app-v-composer-link-shortener
                *ngIf="(selectedPanel$ | async) === panelType.LINK"
                (linkShortened)="closeSidePanel()"
              ></app-v-composer-link-shortener>
              <app-v-composer-suggestions
                *ngIf="(selectedPanel$ | async) === panelType.SUGGESTIONS"
              ></app-v-composer-suggestions>
              <app-v-composer-interesting-content
                *ngIf="(selectedPanel$ | async) === panelType.CONTENT"
                (contentSelected)="contentSelected($event)"
              ></app-v-composer-interesting-content>
              <app-v-composer-coupons
                *ngIf="(selectedPanel$ | async) === panelType.COUPON"
                (couponSelected)="couponSelected($event)"
              ></app-v-composer-coupons>
              <app-v-composer-emoji-picker
                *ngIf="(selectedPanel$ | async) === panelType.EMOJI"
                (emojiSelected)="handleEmoji($event)"
              ></app-v-composer-emoji-picker>
              <app-v-composer-templates
                *ngIf="(selectedPanel$ | async) === panelType.TEMPLATES"
                (templateSelected)="templateSelected($event)"
              ></app-v-composer-templates>
              <app-post-previews *ngIf="(selectedPanel$ | async) === panelType.PREVIEW"></app-post-previews>
              <app-v-composer-dynamic-content
                *ngIf="(selectedPanel$ | async) === panelType.DYNAMIC_CONTENT"
              ></app-v-composer-dynamic-content>
            </glxy-page>
          </div>
        </mat-sidenav>
      </mat-sidenav-container>
    </mat-card>
  }
  <!-- Uncomment it later if custom overlay is needed. -->
  <!-- <app-custom-overlay
    *ngIf="displayTemplateHint$ | async"
    [spotlightTarget]="textBoxDimensions$ | async"
    [hintTitle]="'COMPOSER.TEMPLATE_PICKER.OVERLAY_DIALOG.TITLE' | translate"
    [hintDescription]="'COMPOSER.TEMPLATE_PICKER.OVERLAY_DIALOG.DESCRIPTION' | translate"
    (overlayClicked)="onOverlayClicked()"
  ></app-custom-overlay> -->
  <ng-template #composePage>
    <div
      class="main-composer-section"
      [hidden]="!(displaying === Display.BOTH || displaying === Display.COMPOSER_ONLY)"
    >
      <app-managed-post-composer-warning></app-managed-post-composer-warning>

      <div
        #composePost
        class="compose-post"
        [ngClass]="{
          scroll: (customizeByAccounts$ | async) === false,
        }"
      >
        <div class="compose-post-container d-flex flex-c-d g-16">
          <app-multilocation-pro-info *ngIf="brandId && (isProContext$ | async) === false"></app-multilocation-pro-info>
          <div>
            <div class="post-to-title">
              <div class="composer-title p-b-06">
                <mat-icon>send</mat-icon>
                {{ 'COMPOSER.POST_TO' | translate }}
              </div>
            </div>
            <!--SERVICE SELECTOR-->
            <div class="service-selector" *ngIf="!brandId; else multiLocation">
              <app-service-selector
                #serviceSelector
                [highlightNoAccountError]="highlightNoAccountError"
                (connectAccountEvent)="connectAccounts()"
                [isUWM]="isUWM"
              ></app-service-selector>
              <app-disable-message
                class="service-disable-message"
                [ngClass]="{ customize: customizeByAccounts$ | async }"
                *ngIf="(defaultServiceLoading$ | async) === false"
                [postValidation]="serviceSelectorValidator$ | async"
                [highlightError]="highlightErrors"
              ></app-disable-message>
            </div>
            <ng-template #multiLocation>
              <app-brands-filter [isEditing]="isEditing$ | async"></app-brands-filter>
              <div class="composer-title p-b-06">
                <mat-icon>public</mat-icon>
                {{ 'Social networks' | translate }}
              </div>
              <div class="multilocation-container">
                <div class="multilocation-container">
                  <app-brand-social-networks
                    [isEditing]="isEditing$ | async"
                    [socialService]="postService.FACEBOOK"
                  ></app-brand-social-networks>
                  <app-brand-social-networks
                    *ngIf="featuresService.instagramMultilocationEnabled$ | async"
                    [isEditing]="isEditing$ | async"
                    [socialService]="postService.INSTAGRAM"
                  ></app-brand-social-networks>
                  <app-brand-social-networks
                    *ngIf="featuresService.linkedinMultilocationEnabled$ | async"
                    [isEditing]="isEditing$ | async"
                    [socialService]="postService.LINKEDIN"
                  ></app-brand-social-networks>
                  <app-brand-social-networks
                    [isEditing]="isEditing$ | async"
                    [socialService]="postService.GMB"
                  ></app-brand-social-networks>
                </div>
                <app-disable-message
                  class="disable-message-bar"
                  [postValidation]="brandsServiceLimitValidator$ | async"
                  [highlightError]="highlightErrors"
                ></app-disable-message>
              </div>
            </ng-template>
          </div>

          <!--CONTENT CONTAINER-->
          <ng-container *ngIf="(customizeByAccounts$ | async) === false">
            <div>
              <app-compose-text
                *ngIf="(workFlowType$ | async) !== WorkflowType.STORY_WORKFLOW"
                [brandId]="brandId"
                [highlightError]="highlightErrors"
                [content]="templateContent"
                (customizeBackEvent)="customizeBackEvent($event)"
                [longVideoWorkflow]="(workFlowType$ | async) === WorkflowType.LONG_VIDEO_WORKFLOW"
              ></app-compose-text>
              <!--IMAGE UPLOADER-->
              <app-media-uploader
                [isSoloDisplay]="displaying === Display.COMPOSER_ONLY"
                (uploadClicked)="openMediaPanel(0)"
                (freeImageButtonClicked)="openMediaPanel(2)"
                (cropImageButtonClicked)="openImageCropperPanel($event)"
              ></app-media-uploader>
              <app-disable-message
                class="disable-message-bar"
                [postValidation]="mediaUploadValidator$ | async"
                [highlightError]="highlightErrors"
              ></app-disable-message>
              <app-disable-message
                class="disable-message-bar"
                [postValidation]="imageUsedPreviouslyValidator$ | async"
                [highlightError]="highlightErrors"
              ></app-disable-message>
              @if ((currSelectedCustomization$ | async)?.postType$$.getValue() === POST_TYPE.POST_TYPE_STORIES) {
                <app-disable-message
                  class="disable-message-bar"
                  [postValidation]="invalidInstagramImage$ | async"
                  [highlightError]="highlightErrors"
                ></app-disable-message>
              }
              <app-disable-message
                class="disable-message-bar"
                [postValidation]="gifsOnInstagramValidator$ | async"
                [highlightError]="highlightErrors"
              ></app-disable-message>
              <app-disable-message
                class="disable-message-bar"
                [postValidation]="youtubeMediaValidator$ | async"
                [highlightError]="highlightErrors"
              ></app-disable-message>
            </div>

            <!--POST TYPE-->
            <app-post-type-selector
              *ngIf="
                ((featuresService.composerWithHideDraft$ | async) ||
                  (featuresService.composerWithHideDraftMulti$ | async)) &&
                (composerStore.isBundleAIPost$ | async) === false &&
                (composerStore.isCSVUploadedPost$ | async) === false
              "
            ></app-post-type-selector>
          </ng-container>
          <ng-container *ngIf="(customizeByAccounts$ | async) && (featuresService.composerWithHideDraft$ | async)">
            <app-customize-content
              [highlightError]="highlightErrors"
              (uploadMediaEvent)="openMediaPanelFromCustomize($event)"
            ></app-customize-content>
          </ng-container>

          <!--DATE SELECTOR-->
          <div
            class="publish-section"
            *ngIf="
              (composerStore.isBundleAIPost$ | async) === false && (composerStore.isCSVUploadedPost$ | async) === false
            "
          >
            <div class="select-date">
              <div>
                <app-date-time-selector
                  class="composer-date-time-title"
                  *ngIf="composerStore.postModeSelected() !== POST_MODE.POST_NOW"
                  [date]="scheduledDate$ | async"
                  (dateChange)="onDateChange($event)"
                  [postType]="setPostType()"
                  [mode]="DateUsageModes.COMPOSER"
                ></app-date-time-selector>
              </div>
            </div>

            <app-disable-message
              class="scheduled-post-count"
              [postValidation]="scheduledPostValidator$ | async"
              [highlightError]="highlightErrors"
            ></app-disable-message>
            <app-disable-message
              class="disable-message-bar align-date-time p-0"
              [postValidation]="schedulerValidator$ | async"
              [highlightError]="highlightErrors"
            ></app-disable-message>
          </div>
          <app-advanced-options
            *ngIf="
              ((instagramSelected$ | async) ||
                (tiktokSelected$ | async) ||
                (youtubeSelected$ | async) ||
                (gmbSelected$ | async)) &&
              (workFlowType$ | async) === WorkflowType.POST_WORKFLOW
            "
            [brandId]="brandId"
            [highlightError]="highlightErrors"
            [instagramSelected$]="instagramSelected$"
            [tiktokSelected$]="tiktokSelected$"
            [youtubeSelected$]="youtubeSelected$"
            [gmbSelected$]="gmbSelected$"
            [rootCustomizationsFailingValidators$]="rootCustomizationsFailingValidators$"
          ></app-advanced-options>
        </div>
      </div>

      <!--  DELETE LATER. Customize by account at top location -->
      <ng-container
        *ngIf="(customizeByAccounts$ | async) && (featuresService.composerWithHideDraft$ | async) === false"
      >
        <app-customize-content
          [highlightError]="highlightErrors"
          (uploadMediaEvent)="openMediaPanelFromCustomize($event)"
        ></app-customize-content>
      </ng-container>

      <!--Actions using SL and ML with no two stages-->
    </div>
  </ng-template>
  <ng-template #previewPage>
    <div
      class="previews"
      [hidden]="!(displaying === Display.BOTH || displaying === Display.PREVIEW_ONLY)"
      [ngClass]="{ 'on-narrow-screen': displaying === Display.BOTH }"
    >
      <app-post-previews
        [ngClass]="{ 'two-stage': (displayTwoStage$ | async) || (isMobile$ | async) }"
      ></app-post-previews>
    </div>
  </ng-template>
  <ng-template #composerFooter>
    <mat-card-actions class="composer-footer" [class.mobile-width]="!isMobile">
      <div class="left-actions">
        <button
          mat-button
          *ngIf="
            (showTemplate$ | async) &&
            (composerStore.isBundleAIPost$ | async) === false &&
            (composerStore.isCSVUploadedPost$ | async) === false
          "
          (click)="showNewTemplatePrompt()"
          [disabled]="shouldDisableDraftButton$ | async"
        >
          {{ templateButtonText$ | async }}
        </button>
      </div>
      <div class="right-actions">
        <button
          mat-flat-button
          *ngIf="showPostButton"
          color="primary"
          [disabled]="shouldDisableSubmitButton$ | async"
          (click)="handleSubmitPost()"
        >
          {{ getButtonText() | async }}
        </button>
      </div>
    </mat-card-actions>
  </ng-template>
</div>
