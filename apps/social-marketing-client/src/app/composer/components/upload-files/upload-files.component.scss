@use 'design-tokens' as *;

:host {
  height: 100%;
  max-height: 180px;
  width: 100%;

  .media-upload-input {
    display: none;
  }
  .dragover {
    color: $blue;
    border-color: $blue;

    span {
      color: $blue;
    }
  }
  .image-upload {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    height: 100%;
    max-height: 193px;
    font-size: 14px;
    border: 2px dashed #e0e0e0;
    transition:
      color 0.3s,
      border-color 0.3s;
    cursor: pointer;

    &.large {
      font-size: 18px;
      .icon {
        font-size: 60px;
        height: 60px;
        width: 60px;
      }
    }

    span {
      color: $primary-text-color;
      transition: color 0.3s;

      &.button {
        display: inherit;
        flex-flow: inherit;
        align-items: inherit;
        justify-content: inherit;
        gap: 6px;
      }
    }
    .icon {
      color: $grey;
    }
    .helper-text {
      color: $dark-grey;
    }
  }
}

.text-center {
  text-align: center;
}
