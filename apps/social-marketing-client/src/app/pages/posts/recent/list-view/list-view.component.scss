@use 'design-tokens' as *;

.post-account {
  padding: 10px;
  display: flex;
  flex-direction: row;

  .single-mat-card-avatar {
    justify-content: flex-start;
  }
  .link {
    justify-content: flex-end;
  }
}

.account-cell {
  .single-mat-card-avatar {
    display: flex;

    .profile-image {
      height: 40px;
      width: 40px;
      border-radius: 50%;
      flex-shrink: 0;
      object-fit: cover;
    }

    .social-icon {
      position: relative;
      right: 10px;
      align-self: flex-end;
      border-radius: 100%;
      font-size: 8px;
      height: 20px;
      width: 20px;
      min-width: 20px;
    }
  }

  .serviceCount {
    height: 35px;
    width: 35px;
    background-color: #1d88e5;
    border-radius: 50%;
    display: inline-block;
    text-align: center;
    color: white;
    padding-top: 8px;
  }

  .title {
    font-size: 14px;
    font-weight: 500;
    color: $primary-text-color;
    padding-left: 12px;
  }

  .subtitle {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    margin-top: 4px;
    color: rgba(0, 0, 0, 0.54);
    padding-left: 12px;
  }
}

.content-container {
  height: 100%;
  width: 100%;
}

.post-content {
  height: 100%;
  width: 100%;
  display: flex;

  .image {
    justify-content: flex-start;
    height: 55px;
    width: 70px;
    padding: 4px;
    object-fit: cover;
    border-radius: 5px;
  }

  .video {
    height: 55px;
    width: 70px;
    padding: 4px;
    object-fit: cover;
    border-radius: 5px;
    vertical-align: bottom;
  }

  .post-text {
    justify-content: flex-end;
    padding: 10px;
  }
}
