@use 'design-tokens' as *;
@import '../../../../../../../../core/breaks.scss';

:host:nth-of-type(odd) {
  .review-item-container {
    background: $primary-background-color;
  }
}
.review-item-container {
  display: flex;
  flex-flow: column;
  padding: 8px 0;

  .main-container {
    display: flex;
    flex: 1;
    flex-flow: column;

    .title-container {
      display: flex;
      flex-flow: row;
      flex: 1;

      @include respond-to(mobile) {
        padding-bottom: 8px;
        flex-wrap: wrap;
      }

      .icon {
        flex: 0 0 50px;

        img {
          height: 32px;
          width: 32px;
          border-radius: 50%;
          margin: 3px 8px;
        }
      }
      .details-container {
        flex: 1;
        .title {
          @include respond-to(mobile) {
            line-height: 15px;
            padding-bottom: 8px;
          }
        }
        .subtitle {
          color: #666666;
          font-size: 12px;
          line-height: 12px;
        }
      }
      .star-container {
        flex: 0 0 100px;
        font-size: 14px;
        color: #f7cb16;

        @include respond-to(mobile) {
          flex-basis: 100%;
          font-size: 25px;
          margin-top: 4px;
        }

        mat-icon {
          font-size: 18px;
          height: 18px;
          width: 18px;

          @include respond-to(mobile) {
            font-size: 25px;
            height: 25px;
            width: 25px;
          }
        }
      }
    }
    .content-container {
      padding: 4px 8px;
      font-size: 12px;
      line-height: 15px;
      padding-left: 50px;

      @include respond-to(mobile) {
        padding-left: 4px;
      }
    }
  }
}
