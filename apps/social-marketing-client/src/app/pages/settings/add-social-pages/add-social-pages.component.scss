@use 'design-tokens' as *;

.add-pages {
  padding: 10px;
  max-width: 1400px;
  margin: 0 auto;

  h1 {
    font-size: 2rem;
    margin: 10px 0;
  }
  h3 {
    font-size: 16px;
  }
  h4 {
    font-size: 14px;
    margin-bottom: 10px;
  }
  table {
    width: 100%;
    font-size: 15px;
    border-collapse: collapse;

    th {
      border-bottom: 1px solid $gray;
    }
    td,
    th {
      padding: 15px 5px;
      text-align: left;
      vertical-align: middle;
    }
  }

  ::ng-deep .mat-checkbox-inner-container {
    margin-top: 1px;
  }

  mat-card {
    padding: 15px;
    margin-bottom: 10px;
    h3 {
      font-weight: 500;
    }

    mat-card-content {
      overflow-x: scroll;
    }

    mat-card-actions {
      padding: 10px;
      margin: 0 -15px -15px;
      background-color: $glxy-blue-50;
    }
  }

  .banner {
    display: flex;
    padding: 10px;
    align-items: center;
    margin: 5px 0 5px 0;
    border: 1px solid #4caf50;
    border-radius: 2px;

    .tooltip-icon {
      font-family: 'Platform Icons';
      speak: none;
      font-style: normal;
      font-weight: normal;
      font-variant: normal;
      text-transform: none;
      line-height: 1;
      -webkit-font-smoothing: antialiased;
      content: '';
      display: inline-block;
    }

    .green-icon {
      color: #4caf50;
    }

    .message {
      font-size: 0.85rem;
      color: $primary-text-color;
      padding-left: 10px;
    }
  }

  .alert-success {
    background-color: #e1f4e1;
  }

  .loading-message {
    margin: 15px;
  }
  .social-page-link {
    padding-left: 18px;
  }

  .loader {
    text-align: center;
    padding-top: 10px;

    .loader-text {
      color: $dark-gray;
      font-style: italic;
    }

    .spinner-icon {
      font-size: 30px;
    }
  }

  .cancel-action {
    padding-left: 26px;
    font-weight: bold;
    font-size: 14px;
  }

  .selector-footer {
    padding-top: 10px;
    border-top: 1px solid $light-gray;
  }

  .page {
    &-title {
      color: $black;
      padding-left: 8px;
      .disabled {
        color: $gray;
      }
      .green-text {
        color: $green;
      }
      .red-text {
        color: $red;
      }
    }
    &-subtitle {
      padding: 5px 0px 0px 0px;
      font-size: 12px;
      font-weight: bold;
    }
  }

  .linkedIn-page-disable {
    color: $black;
    padding-left: 8px;
    opacity: 0.38;
    .disabled {
      color: rgba(0, 0, 0, 0.54);
    }
  }

  .no-pages p {
    font-size: 15px;
  }
  .card-action {
    display: flex;
    justify-content: space-between;
    .card-action-fill {
      flex-grow: 1;
    }
    .spinner {
      margin: 0 30px;
    }
  }
  .table-filter-row {
    display: flex;
    align-items: center;
    .filter {
      flex-grow: 1;
      mat-form-field {
        width: 100%;

        ::ng-deep {
          .mat-form-field-suffix {
            position: absolute;
            top: 16px;
            right: 0;
            width: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }

  .incorrect-permissions {
    display: flex;
    align-items: center;
    font-size: 12px;
    background-color: $light-red;
    border-left: 4px solid $dark-red;
    mat-icon {
      margin-left: 2px;
      color: $dark-red;
    }
    .incorrect-permissions-message {
      margin-left: 4px;
    }
  }

  .connected {
    font-style: italic;
  }

  .token-broken {
    color: red;
  }

  mat-card-title {
    display: flex;
    flex-flow: row nowrap;
    margin-bottom: 4px;
    font-size: 14px;
    font-weight: normal;

    @media screen and (max-width: 500px) {
      flex-flow: row wrap;
    }
  }

  mat-card-subtitle {
    margin-bottom: 0px;
  }
}
mat-spinner {
  margin: auto;
}
