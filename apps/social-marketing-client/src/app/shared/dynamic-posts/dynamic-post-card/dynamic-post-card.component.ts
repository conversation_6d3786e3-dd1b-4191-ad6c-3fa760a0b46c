import { Overlay } from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MatDialog } from '@angular/material/dialog';
import { GalaxyDateAdapter, MY_DATE_FORMATS } from '@vendasta/galaxy/datepicker/src/date-adapter';
import { getLocale } from '@vendasta/galaxy/utility/locale';
import { Location, MultilocationPost } from '@vendasta/social-posts';
import { filter, mapTo, take, tap } from 'rxjs';
import { BrandsService } from '../../../composer/components/brands/brands.service';
import { BulkUploadPostsService } from '../../../composer/components/bulk-upload/bulk-upload-posts.service';
import { ComposerComponent } from '../../../composer/composer.component';
import { NEW_POST_ID, POSTHOG_KEYS } from '../../../composer/constants';
import { ComposerSettings, FileType, typeServiceMlSelected } from '../../../composer/interfaces';
import { GmbOptions } from '../../../composer/models/gmb-options';
import { SocialService } from '../../../composer/post';
import { mediaentriesToUploadedMedia } from '../../../composer/shared-methods';
import { CommonUtils } from '../../../composer/shared/utils/common-utils';
import { ConfigService } from '../../../core';
import { ComposerSettingsService } from '../../../core/composer-settings/composer-settings.service';
import { Customization } from '../../../core/post/customization';
import { PostScheduleType, SocialConnection } from '../../../pages/posts/ai-bundle-posts/model';
import { DeletePostDialogComponent } from '../../post/delete-post-dialog/delete-post-dialog.component';
import { PostableService } from '../../social-service/social-service.service';
import {
  DynamicMedia,
  MAX_CONTENT_LENGTH,
  MediaType,
  MLPosts,
  MLSocialNetworkType,
  PostContent,
} from '../dynamic-posts-models';
import { DynamicPostsService } from '../dynamic-posts.service';
import { ComposerStoreService } from '../../../composer/composer-store.service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';

@Component({
  selector: 'app-dynamic-post-card',
  standalone: false,
  providers: [
    { provide: DateAdapter, useClass: GalaxyDateAdapter },
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMATS },
    { provide: MAT_DATE_LOCALE, useValue: getLocale() },
  ],
  templateUrl: './dynamic-post-card.component.html',
  styleUrl: './dynamic-post-card.component.scss',
})
export class DynamicPostCardComponent implements OnInit {
  @Input() postContent;
  @Input() parentIndex;

  @Input() socialNetworks: SocialConnection[];
  @Output() postEdited = new EventEmitter();
  @Output() postDeleted = new EventEmitter();
  @Output() editClicked = new EventEmitter();
  defaultProfileImageUrl: string;
  fbIcon: string;
  gmbIcon: string;
  igIcon: string;
  liIcon: string;
  networkType = MLSocialNetworkType;
  isInvalidTime = false;
  isExpanded = false;
  selectedIndex = 0;
  editedPostIndex = 0;
  isMLPost = false;
  value: Date;
  selectedOption: any;
  preferredNetworkOrder = [
    this.networkType.FACEBOOK,
    this.networkType.INSTAGRAM,
    this.networkType.LINKEDIN,
    this.networkType.GMB,
    this.networkType.GMBSID,
  ];

  postStatusOptions: { key: string; value: string }[] = Object.keys(PostScheduleType).map(
    (
      key,
    ): {
      key: string;
      value: string;
    } => ({
      key,
      value: PostScheduleType[key],
    }),
  );

  constructor(
    public optimizedPostsService: DynamicPostsService,
    private config: ConfigService,
    private composerSettingsService: ComposerSettingsService,
    private overlayService: Overlay,
    private dialog: MatDialog,
    private bulkUploadPostsService: BulkUploadPostsService,
    private brandsService: BrandsService,
    private composerStore: ComposerStoreService,
    private productAnalyticsService: ProductAnalyticsService,
  ) {}

  ngOnInit() {
    if (!this.config?.config()?.is_digital_agent) {
      this.postStatusOptions = this.postStatusOptions.filter((ele) => ele.value !== PostScheduleType.HIDDEN_DRAFT);
    }
    this.isMLPost = this.postContent?.some((m) => m?.isML);

    if (!this.isMLPost) {
      this.postContent = this.optimizedPostsService.mapNetworksWithPosts(this.socialNetworks, this.postContent);
    }

    if (this.isMLPost) {
      this.postStatusOptions = this.postStatusOptions.filter((ele) => ele.value !== PostScheduleType.DRAFT);
      this.selectedOption = this.postStatusOptions.find((v) => v).value;
    }

    this.defaultProfileImageUrl = CommonUtils.getImageSrc('social-icons/default.png');
    this.fbIcon = CommonUtils.getImageSrc('social-icons/facebook.png');
    this.gmbIcon = CommonUtils.getImageSrc('social-icons/google_my_business.png');
    this.igIcon = CommonUtils.getImageSrc('social-icons/instagram.png');
    this.liIcon = CommonUtils.getImageSrc('social-icons/linkedin.png');
  }

  deletePost(postIndex: number) {
    this.postDeleted.emit({
      index: postIndex,
      isLastPost: this.postContent.length === 0,
    });
  }

  showDeleteConfirmationDialog(postIndex: number): void {
    const isLastPost = this.postContent.length === 1;
    this.dialog
      .open(DeletePostDialogComponent, {
        data: {
          type: isLastPost ? 'lastpost' : 'csv',
        },
        width: '700px',
        autoFocus: false,
      })
      .afterClosed()
      .subscribe((confirmed: boolean) => {
        if (confirmed) {
          this.deletePost(postIndex);
        }
      });
  }

  onTabChange(index: number) {
    this.selectedIndex = index;
  }

  handleContentEdited = (posts: MLPosts) => {
    const postIndex = this.isMLPost ? this.editedPostIndex : this.selectedIndex;
    this.postContent[postIndex] = {
      ...this.postContent[postIndex],
      postText: posts[0].postText,
      Medias: posts[0]?.mediaEntries.map(
        (media) =>
          ({
            mediaType: media.mediaType.toUpperCase() as MediaType,
            mediaUrl: media.mediaUrl,
          }) as DynamicMedia,
      ),
      postType: posts[0]?.postType,
      mlSocialNetworkType: this.getNetworkType(posts?.locations),
      locations: posts?.locations,
      scheduleDate: posts[0]?.scheduled,
      gmbCustomization: posts[0]?.gmbPostCustomization
        ? {
            event: {
              title: posts[0]?.gmbPostCustomization.title || '',
              start: posts[0]?.gmbPostCustomization.eventStart || null,
              end: posts[0]?.gmbPostCustomization.eventEnd || null,
            },
            action: {
              type: posts[0]?.gmbPostCustomization.ctaType || '',
              linkUrl: posts[0]?.gmbPostCustomization.linkUrl || '',
            },
          }
        : undefined,
      filter: this.brandsService.filters$$.getValue(),
    };
    this.postEdited.emit({
      post: this.postContent[postIndex],
      index: postIndex,
    });
  };

  now(): Date {
    return new Date();
  }

  private mapSSIDNetwork(ssids: string[]): Partial<Record<SocialService, boolean>> {
    if (ssids?.length) {
      const networkMap: Partial<Record<SocialService, MLSocialNetworkType>> = {
        [SocialService.FACEBOOK]: MLSocialNetworkType.FACEBOOK,
        [SocialService.GMB]: MLSocialNetworkType.GMB,
        [SocialService.INSTAGRAM]: MLSocialNetworkType.INSTAGRAM,
        [SocialService.LINKEDIN]: MLSocialNetworkType.LINKEDIN,
        [SocialService.LINKEDIN_COMPANY]: MLSocialNetworkType.LINKEDIN,
      };

      const result: Partial<Record<SocialService, boolean>> = {} as Partial<Record<SocialService, boolean>>;

      for (const [service, mlValue] of Object.entries(networkMap)) {
        result[service as SocialService] = ssids.some((item) => item.startsWith(mlValue));
      }
      return result;
    }
  }

  private getNetworkType(post: Location[]): MLSocialNetworkType[] {
    const activeNetworkTypes = new Set<MLSocialNetworkType>();
    post
      ?.flatMap((p) => p.socialServiceIds)
      .forEach((ssid) => {
        if (ssid.startsWith(this.networkType.FACEBOOK)) {
          activeNetworkTypes.add(this.networkType.FACEBOOK);
        } else if (ssid.startsWith(this.networkType.GMBSID) || ssid.startsWith(this.networkType.GMB)) {
          activeNetworkTypes.add(this.networkType.GMB);
        } else if (ssid.startsWith(this.networkType.INSTAGRAM)) {
          activeNetworkTypes.add(this.networkType.INSTAGRAM);
        } else if (ssid.startsWith(this.networkType.LINKEDIN)) {
          activeNetworkTypes.add(this.networkType.LINKEDIN);
        }
      });
    return Array.from(activeNetworkTypes).sort(
      (a, b) => this.preferredNetworkOrder.indexOf(a) - this.preferredNetworkOrder.indexOf(b),
    );
  }

  private getNetworkNames(locations: Location[]): typeServiceMlSelected {
    return {
      facebook: this.isServiceSelected(this.networkType.FACEBOOK, locations),
      gmb:
        this.isServiceSelected(this.networkType.GMBSID, locations) ||
        this.isServiceSelected(this.networkType.GMB, locations),
      instagram: this.isServiceSelected(this.networkType.INSTAGRAM, locations),
      linkedin: this.isServiceSelected(this.networkType.LINKEDIN, locations),
    };
  }

  private isServiceSelected(network: MLSocialNetworkType, loc: Location[]): boolean {
    return loc?.flatMap((p) => p.socialServiceIds).some((ssid) => ssid.startsWith(network));
  }

  async editPosts(post: PostContent, postIndex: number) {
    this.editClicked.emit();
    const postId = NEW_POST_ID;
    const brandId = this.config.brandId;
    this.editedPostIndex = postIndex;
    let composerSettings: ComposerSettings;
    if (brandId) {
      const multilocationPost = new MultilocationPost();
      multilocationPost.brandId = brandId;
      multilocationPost.originalText = post?.postText;
      multilocationPost.originalScheduledDate =
        post?.scheduleDate instanceof Date ? post?.scheduleDate : new Date(post?.scheduleDate);
      multilocationPost.originalMedia = post?.Medias.map((media) => media.mediaUrl);
      const media = [];
      if (multilocationPost.originalMedia && multilocationPost.originalMedia.length) {
        post?.Medias.map((m) => {
          if (m?.mediaType === MediaType.IMAGE) {
            media.push({ url: m?.mediaUrl, fileType: FileType.IMAGE });
          } else if (m?.mediaType === MediaType.VIDEO) {
            media.push({ url: m?.mediaUrl, fileType: FileType.VIDEO });
          } else if (m?.mediaType === MediaType.GIF) {
            media.push({ url: m?.mediaUrl, fileType: FileType.GIF });
          }
        });
      }
      const gmbSelected =
        this.isServiceSelected(this.networkType.GMB, post?.locations) ||
        this.isServiceSelected(this.networkType.GMBSID, post?.locations);

      const gmbOptions = new GmbOptions();
      if (post?.gmbCustomization) {
        if (
          post?.gmbCustomization?.event?.title &&
          post?.gmbCustomization?.event?.start &&
          post?.gmbCustomization?.event?.end
        ) {
          gmbOptions.makeEvent = true;
          gmbOptions.eventOptions = {
            title: post.gmbCustomization.event.title,
            startDate: post.gmbCustomization.event.start,
            endDate: post.gmbCustomization.event.end,
          };
        }
        if (post.gmbCustomization?.action?.type || post.gmbCustomization?.action?.linkUrl) {
          gmbOptions.addCta = true;
          gmbOptions.ctaOptions = {
            action: post.gmbCustomization.action.type,
            ctaUrl: post.gmbCustomization.action.linkUrl,
          };
        }
      }

      composerSettings = {
        activeLocations: post?.locations,
        selectedMLlocations: this.getNetworkNames(post?.locations),
        groupedCustomization: new Customization({
          postText: multilocationPost.originalText,
          scheduledDate: multilocationPost.originalScheduledDate,
          uploadedMediaObjects: media,
          mediaEntries: post?.Medias || [],
          gmbOptions: gmbSelected ? gmbOptions : null,
        }),
        multilocationId: 'FAKE-ML-ID', // Giving a fake multilocationId to avoid error
        isCSVUploadedPost: true,
        composerCallback: this.handleContentEdited,
        brandId: brandId,
        postType: post.postType,
        filter: post?.filter,
      } as ComposerSettings;
    } else {
      this.productAnalyticsService.trackEvent(POSTHOG_KEYS.SOCIAL_CAMPAIGN_EDIT_POST, 'user', 'click', 0);
      const services = new Map<PostableService, string>();
      const bundleServices = Array.isArray(post.socialConnection) ? post.socialConnection : [post.socialConnection];
      bundleServices.forEach((service) => {
        services.set(service as PostableService, postId);
      });

      composerSettings = {
        composerCallback: this.handleContentEdited,
      };
      const createCustomization = (post: PostContent, services: Map<PostableService, string>) => {
        const { postText: postText, Medias } = post;
        const mediaEntries = Medias || [];
        const uploadedMedia = mediaentriesToUploadedMedia(mediaEntries);
        return new Customization({
          postText,
          mediaEntries,
          uploadedMediaObjects: uploadedMedia,
          services,
        });
      };

      const customization = createCustomization(post, services);
      composerSettings = {
        ...composerSettings,
        groupedCustomization: customization,
      };
    }

    brandId ? this.showComposer(composerSettings) : this.composerSettingsService.showComposer(composerSettings);
  }

  public showComposer(setting?: ComposerSettings): void {
    const overlayRef = this.overlayService.create();
    const portal = new ComponentPortal(ComposerComponent);
    const compRef = overlayRef.attach(portal);
    const ssids = [];

    if (this.config.brandId) {
      setting.activeLocations?.forEach((location) => {
        location?.socialServiceIds.forEach((ssid) => {
          ssids.push(ssid);
        });
      });
      compRef.instance.brandId = this.config.brandId;
      compRef.instance.showClose = true;
      compRef.instance.setAgidOnStore();
      this.config.getAgidFromBrand(this.config.brandId);
      compRef.instance.updateBrandFilter(setting.filter);
      compRef.instance.selectedMLNetworks(this.mapSSIDNetwork(ssids));
    }

    compRef.instance.initialize(ssids);

    compRef.instance.showComposer(setting);

    compRef.instance.visibleEvent.pipe(
      filter((isComposerVisible) => !isComposerVisible),
      mapTo(true),
      tap(() => {
        overlayRef.detach();
      }),
      take(1),
    );
  }

  validateDateTime(index?: any): void {
    const now = new Date();
    const effectiveIndex = index ?? this.selectedIndex;
    let scheduledDate = this.postContent[effectiveIndex]?.scheduleDate;

    if (scheduledDate && new Date(scheduledDate) < now) {
      this.isInvalidTime = true;
      scheduledDate = now; // Reset to current time if needed
    } else {
      this.isInvalidTime = false;
    }

    this.postEdited.emit({
      post: this.postContent[effectiveIndex],
      index: effectiveIndex,
      type: 'date',
    });
  }

  protected readonly MAX_CONTENT_LENGTH = MAX_CONTENT_LENGTH;
}
