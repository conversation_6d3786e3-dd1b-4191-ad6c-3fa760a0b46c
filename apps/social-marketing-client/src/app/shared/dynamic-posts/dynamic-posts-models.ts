import { PostInterface, PostType } from '@vendasta/composer';
import { PostScheduleType, SocialConnection } from '../../pages/posts/ai-bundle-posts/model';
import { SocialService } from '../../composer/post';
import { Location, SSIDPostType } from '@vendasta/social-posts';

export const MAX_CONTENT_LENGTH = 200;
export interface PostContent {
  socialConnection?: SocialConnection;
  socialNetwork?: SocialService;
  mlSocialNetworkType?: MLSocialNetworkType[];
  postText: string;
  postType?: PostType;
  Medias?: DynamicMedia[];
  postMode?: PostScheduleType;
  scheduleDate: Date;
  isEditing?: boolean;
  isML?: boolean;
  locations?: Location[];
  gmbCustomization?: gmbCustomization;
  mlPostTypes?: SSIDPostType[];
  filter?: Map<string, string[]>;
}

export interface MLPosts {
  posts: PostInterface[];
  locations: Location[];
}

export interface Posts {
  title?: '';
  postContent: PostContent[];
}

export interface DynamicMedia {
  mediaUrl: string;
  mediaType: MediaType;
}

export enum MediaType {
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
  GIF = 'GIF',
}

export enum MLSocialNetworkType {
  FACEBOOK = 'FBP',
  GMB = 'account',
  GMBSID = 'GMB',
  INSTAGRAM = 'IGU',
  LINKEDIN = 'LI',
}

export interface gmbCustomization {
  event: Event;
  action: Action;
}

export interface Event {
  title: string;
  start: Date;
  end: Date;
}

export interface Action {
  type: string;
  linkUrl: string;
}
