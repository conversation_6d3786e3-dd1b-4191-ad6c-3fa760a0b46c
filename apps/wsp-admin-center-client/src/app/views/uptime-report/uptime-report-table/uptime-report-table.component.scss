@use 'design-tokens' as *;

.sites-list-container {
  min-height: min-content;
  min-width: 720px;
  margin: 0 auto;
  background: none;
  background-color: white;
  box-shadow:
    0 3px 1px -2px rgba(0, 0, 0, 0.2),
    0 2px 2px 0 rgba(0, 0, 0, 0.14),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);
  padding: 0px;

  .mat-column-account {
    flex: 0 0 20%;
  }
  .mat-column-url {
    flex: 0 0 30%;
  }

  mat-header-cell {
    font-weight: bold;
    color: black;
    border-bottom-width: 0;
  }

  .data-column-cell {
    text-align: center;
    min-width: 5%;
  }

  .actions-column-cell {
    text-align: right;
    min-width: 100px;
  }

  .plugin-clickable {
    cursor: pointer;
    color: $primary-color;
  }

  .total-table-spinner {
    margin-top: 20px;
    margin-left: 40%;
  }

  .website-logo {
    float: left;
    padding: 0 5px;
    height: 24px;
  }

  .wordpress-logo {
    float: left;
    padding: 0 5px;
    height: 24px;
  }

  .cell-clickable {
    float: right;
    padding: 0 5px;
    cursor: pointer;
    color: $primary-color;
  }

  .logo-clickable {
    float: left;
    padding: 0 5px;
    cursor: pointer;
    color: $primary-color;
  }

  .wordpress-clickable {
    float: right;
    padding: 0 5px;
    cursor: pointer;
    color: $primary-color;
  }

  .table-info {
    overflow-x: auto;
    ::ng-deep mat-cell {
      div {
        text-align: left;
      }
    }
  }

  #site-list {
    text-align: left;
    flex-basis: auto;
  }

  .tooltip {
    display: inline-block;
  }

  .tooltip .tooltiptext {
    visibility: hidden;
    width: 250px;
    background-color: black;
    color: #fff;
    text-align: left;
    border-radius: 6px;
    padding: 5px;

    /* Position the tooltip */
    position: absolute;
    z-index: 1;
  }

  .tooltip:hover .tooltiptext {
    visibility: visible;
  }
  .success-icon {
    color: #4caf50;
  }
  .error-icon {
    color: red;
  }
}
:host {
  display: block;
}
.badges {
  .glxy-badge {
    margin: 0 $spacing-1 $spacing-1 0;
  }
}
button {
  vertical-align: middle;
}

.link {
  line-height: $spacing-4;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  text-decoration: none;
  color: inherit;
}

.logo {
  float: left;
  height: $spacing-4;
  margin-right: 3px;
}

.dashboard-text {
  display: inline;
  word-break: break-word;
}

.side {
  top: 0;
  position: fixed;
  width: 400px;
}

mat-drawer-content {
  padding: 12px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.side-header {
  background: #f5f5f5;
  padding: 16px 24px;
  height: 56px;
  border-bottom: 1px solid #e0e0e0;
}
.side-content {
  padding: 16px 24px;
  color: $secondary-text-color;
}
glxy-table-content-footer > .mat-mdc-paginator-container {
  display: none;
}

.dates > div {
  width: 100%;
  float: left;
}
.status,
.dates,
.root-cause,
.step-to-resolve {
  padding-bottom: 16px;
  width: 100%;
  float: inline-start;
}
.right {
  position: absolute;
  right: 0;
  top: 0;
}
.domain-link {
  color: $primary-color;
  text-decoration: none;
}

mat-drawer-content {
  padding: 0;
}
.root-cause {
  padding-bottom: 0px;
}
.steps-to-resolve .ng-star-inserted ul {
  margin-top: 0px;
  padding-left: 16px;
}
.step-text {
  padding-bottom: 16px;
}
.uptime-checks-info {
  display: flex;
  align-items: center;
  margin-top: 10px; /* Adjust spacing as needed */
  font-size: 14px; /* Match your design specifications */
}

.uptime-checks-info mat-icon {
  margin-right: 5px; /* Space between icon and text */
}

.update-row {
  display: flex;
}
.update-cell {
  width: 25%;
}

.progress-bar {
  width: 100%;
  height: 16px;
  background-color: #fdd;
  border-radius: 5px;
  margin: 20px 0;
  .up {
    background-color: green;
    height: 100%;
    border-radius: 8px 0px 0px 8px;
    float: left;
  }
  .down {
    background-color: red;
    height: 100%;
    border-radius: 0 8px 8px 0;
    float: right;
  }
}
.progress {
  text-align: center;
  .up {
    width: 50%;
    float: left;
    color: green;
  }
  .down {
    width: 50%;
    float: right;
    color: red;
  }
  .count {
    font-size: 30px;
  }
  .percentage {
    font-size: 20px;
  }
}

.status-text {
  color: $secondary-text-color;
  text-align: center;
  .up {
    width: 50%;
    float: left;
  }
  .down {
    width: 50%;
    float: right;
  }
}
.up-icon {
  color: #4caf50;
  vertical-align: -6px;
}
.down-icon {
  color: red;
  vertical-align: -6px;
}
.deactivate-icon {
  vertical-align: -6px;
  color: #000;
}
.all-icon {
  color: #56b4e9;
}
.update-cell {
  cursor: pointer;
}
.update-cell {
  &.active {
    .mdc-card {
      border-bottom: 2px solid #1976d2;
    }
  }
}
.update-cell {
  &.active {
    .mat-mdc-card {
      .mat-mdc-card-header .mat-mdc-card-title span {
        color: #1976d2;
      }
    }
  }
}
.mat-drawer-content {
  &.mat-drawer-content {
    margin-top: 30px;
    border: 1px solid #ebe7e7;
  }
}
.glxy-table-content-header {
  border-bottom: 1px solid #eaeaea;
}
mat-header-row {
  &.mat-mdc-header-row {
    &.mdc-data-table__header-row {
      &.cdk-header-row {
        &.ng-star-inserted {
          background-color: #ededed !important;
        }
      }
    }
  }
}
.uptime-info-container {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  /* margin: 16px 0; */
  width: 100%;
  left: 10%;
  // position: absolute;
  /* top: 54px; */
  /* z-index: 1;*/
}

.uptime-info {
  font-size: 16px;
  color: $secondary-text-color; // Adjust the text color
  display: flex;
}
.info-icon {
  margin-left: 6px;
}
::ng-deep .info-tooltip {
  font-size: 14px;
}
.domain-link {
  display: flex;
}
.all-websites span {
  display: flex;
}
.globe-icon {
  margin-left: 5px;
  color: #1b76d2;
}
.update-cell {
  .mat-mdc-card:hover {
    background-color: #f5f9fc;
  }
}
#site-list mat-row:hover {
  background-color: #f5f9fc !important;
}
.duration {
  margin-top: 16px;
}
.status {
  display: flex;
}
.mat-mdc-table-sticky-border-elem-right {
  max-width: 6%;
}
::ng-deep .scroll-shadow .glxy-page-toolbar {
  z-index: 1 !important;
}
.status {
  strong {
    margin-right: 5px;
  }
}
