@use 'design-tokens' as *;

$breakpoint-xs: 600px;
$breakpoint-sm: 768px;
$breakpoint-md: 960px;

.tiered-price {
  align-self: baseline;
  margin-bottom: 16px;
}

.price-card-title {
  font-size: 32px;
  font-weight: 400;
  color: $primary-text-color;
  display: flex;
  flex-direction: row;
  padding-top: 4px;
}

.currency-frequency {
  display: flex;
  flex-direction: column;
  padding-left: 4px;
  justify-content: flex-end;
}

.subtitle {
  font-size: 14px;
  font-weight: 400;
  color: $secondary-text-color;
}
