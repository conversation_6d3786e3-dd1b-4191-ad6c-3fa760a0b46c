@use 'design-tokens' as *;

$breakpoint-xs: 600px;
$breakpoint-sm: 768px;

:host {
  display: block;
  font-size: 14px;
  line-height: 1.4;

  * {
    box-sizing: border-box;
  }
}

.va-component-title {
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 24px;
}

.subtitle {
  font-size: 14px;
  font-weight: 400;
  color: $secondary-text-color;
}

.price-card-title {
  font-size: 32px;
  font-weight: 400;
  color: $primary-text-color;
}

.footer {
  font-size: 12px;
  font-weight: 400;
  color: $secondary-text-color;
  margin-top: 16px;
  white-space: normal;
}

.center {
  text-align: center;
}

.pricing-card {
  border: solid;
  border-width: 1px;
  border-radius: 8px;
  border-color: #e0e0e0;
}

.tiered-price-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  @media screen and (max-width: $breakpoint-xs) {
    flex-direction: column;
  }
}

.tiered-price {
  margin-bottom: 16px;
  @media screen and (max-width: $breakpoint-xs) {
    padding: 8px 0;
  }
}
