@use 'design-tokens' as *;
@use 'breaks' as *;

:host ::ng-deep .mat-horizontal-stepper-header-container {
  margin: 0;
}

:host ::ng-deep .mat-horizontal-content-container {
  padding: 0;
}

.step-2-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;

  @include respond-to(mobile) {
    flex-direction: column-reverse;
    justify-content: center;
  }

  a {
    margin-right: 8px;
  }

  .skip-button {
    // justify this button to the left (flex-start)
    margin-right: auto;
    @include respond-to(mobile) {
      margin-right: 0;
    }
  }
}

.skip-button {
  color: $primary-text-color;
}

.schedule-stepper {
  background-color: transparent;
}

.stepper-actions {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;

  @include respond-to(mobile) {
    justify-content: center;
  }

  .step-1-next-button {
    margin-top: 8px;
  }
}
.step-3-next-button {
  color: #ffffff !important;
}

.stepper-actions-column {
  flex-direction: column-reverse;
  align-items: center;
}

.switch-view-link {
  display: block;
  text-align: right;
  font-size: $font-preset-6-size;
  color: $tertiary-font-color;
}

.step-1-next-button-container {
  display: flex;
  width: 100%;
  justify-content: flex-end;
}

.action-bar {
  justify-content: end !important;
}
