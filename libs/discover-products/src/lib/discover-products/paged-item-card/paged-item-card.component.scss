@use 'design-tokens' as *;

mat-card {
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-bottom: 24px;
  border-radius: 4px;
}

.removeStyle {
  text-decoration: none;
}

.card-container {
  height: 100%;
}

.product-card {
  box-shadow: none !important;
  border: 1px solid #e0e0e0;
}

mat-card-title {
  font-size: 16px;
  font-weight: 400;
  color: $primary-text-color;
  white-space: normal;
  max-width: 100%;
  overflow-wrap: anywhere;
}

mat-card-subtitle {
  overflow-wrap: anywhere;
}

mat-card-content {
  color: #9e9e9e;
  font-size: 14px;
  padding-top: 8px;
}

.mat-card-image {
  object-fit: cover;
  height: 150px;
  width: 100%;
}

.d-item-container {
  box-sizing: border-box;
  height: 100%;
  padding: 12px;
  @media screen and (max-width: $media--desktop-medium-minimum) {
    width: 100%;
    padding: 4px;
  }
}

.s-tagline {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.m-tagline {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  @media screen and (max-width: $media--tablet-large-minimum) {
    -webkit-line-clamp: 1;
  }
}

.l-tagline {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.m-flex-container {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  @media screen and (max-width: $media--tablet-large-minimum) {
    flex-direction: row;
  }
}

.m-flex-container > .grid {
  padding: 8px 8px 0px 8px;
}

.flex-container {
  display: flex;
  flex-wrap: nowrap;
}

.flex-container > .grid {
  padding: 8px 8px 0px 8px;
}

.mat-card-small {
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 6px;
}

.background-logo {
  width: 100%;
  height: 100px;
  overflow: hidden;
  background-color: #e0e0e0;

  .defaultBannerImage {
    display: flex;
    height: inherit;
    margin-left: auto;
    margin-right: auto;
  }
  .bannerImage {
    object-fit: contain;
    width: 100%;
    position: relative;
    top: 50%;
    transform: translateY(-50%);
  }
}

.pop-content-container {
  position: relative;
  padding: 0 16px 0px 16px;
  bottom: 33px;
  font-size: 12px;
}

.icon-name-container {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  .title {
    font-size: 16px;
    white-space: nowrap;
    padding: 0 8px 0 8px;
  }
}

.icon {
  border: 1px solid lightgrey;
  border-radius: 4px;
  background-color: white;
}

.chip-wrapper {
  padding: 8px 0 8px 0;
  display: flex;
  flex-direction: row;
}

.chip-list {
  display: flex;
  flex-flow: row wrap;
  overflow: hidden;
  height: 28px;
}

.more-chip-spacer {
  min-width: 77px;
}

.more-chip {
  overflow: visible;
  position: absolute;
}

.mat-mdc-chip {
  flex: 0 0 auto;
  color: grey;
  font-size: 12px;
  min-height: 20px;
}

.tagline {
  padding: 8px 0 8px 0;
}

.key-selling-points-container {
  span {
    font-size: 16px;
    color: #9e9e9e;
  }
  ul {
    padding-left: 16px;
    li {
      padding-bottom: 4px;
    }
  }
}

.popover-container {
  width: 450px;
}

.popover-card {
  padding: 0;
  box-shadow: none;
}

.details-button {
  width: 100%;
  color: #1e88e5;
  background-color: #f6f6f6;
}

.add-to-store {
  width: 100%;
  color: #f6f6f6;
  background-color: #1e88e5;
  margin-top: 8px;
}
