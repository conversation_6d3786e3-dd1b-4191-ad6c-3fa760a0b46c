@use 'design-tokens' as *;

.editor-container {
  background-color: $secondary-background-color;
}

.field-group__label {
  font-weight: 500;
}

.palette {
  display: inline-block;
  vertical-align: top;
  padding: 0;
}

.field-list-search {
  padding: 20px 24px 26px;
}

.field-group {
  background-color: unset;
  padding: 0 24px 24px;
  border-bottom: none;
  border-left: none;
  border-right: none;
}

.field-group__label {
  padding: 24px 0 10px;
}

.available-field {
  padding-top: 10px;
  height: 40px;
  display: inline-flex;
  width: 100%;
}

:host ::ng-deep .mat-expansion-panel-body {
  border-top: none;
  padding: 0;
}

.editor-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.draggable-field__placeholder {
  background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='4' ry='4' stroke='%23E0E0E0FF' stroke-width='3' stroke-dasharray='3%2c7' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
  border-radius: 4px;
  border: unset;
  min-height: 40px;
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow:
    0 5px 5px -3px rgba(0, 0, 0, 0.2),
    0 8px 10px 1px rgba(0, 0, 0, 0.14),
    0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drop-list-dragging {
  pointer-events: none;
}

form-builder-available-field {
  --drag-color: #{$primary-text-color};
  --drag-border-color: #{$field-border-color};
  --drag-background-color: #{$field-background-color};
  --drag-cursor: grab;
}

form-builder-available-field:hover {
  --drag-border-color: #{$glxy-blue-400};
}

.cdk-drag-disabled form-builder-available-field {
  --drag-color: #{$tertiary-text-color};
  --drag-border-color: #{$field-border-disabled-color};
  --drag-background-color: #{$field-background-disabled-color};
  --drag-cursor: not-allowed;
}

.cdk-drop-list-dragging .draggable-field:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

mat-drawer-content {
  width: 100%;
  height: 100%;
  display: flex;
  padding: 36px;
}

.canvas {
  height: 100%;
  min-height: 64px;
  width: 100%;
  max-width: 580px;
  gap: 40px;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
}

.editable-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.submit-button-container {
  padding-bottom: 36px;
}

.submit-button {
  width: fit-content;
}

.add-section-bar {
  border: 1px dashed #{$primary-border-color};
  color: #{$secondary-text-color};
  margin-left: -20px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: default;
}
