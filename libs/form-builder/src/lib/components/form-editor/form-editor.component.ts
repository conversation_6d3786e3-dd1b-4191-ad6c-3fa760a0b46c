import { CdkDragDrop, CdkDragEnter, DragDropModule, moveItemInArray } from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  ComponentRef,
  computed,
  DestroyRef,
  EventEmitter,
  Inject,
  inject,
  Injector,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  signal,
  SimpleChanges,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSidenavModule } from '@angular/material/sidenav';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AuxiliaryDataFieldType } from '@vendasta/account-group';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyWrapModule } from '@vendasta/galaxy/galaxy-wrap';
import { combineLatest, EMPTY, Observable, of, Subject } from 'rxjs';
import {
  catchError,
  debounceTime,
  filter,
  map,
  shareReplay,
  startWith,
  switchMap,
  take,
  takeUntil,
} from 'rxjs/operators';
import { v4 as uuidv4 } from 'uuid';
import { AvailableFieldComponent } from './available-field.component';
import { FormFieldEditorComponent } from './form-field-editor.component';
import { FormFieldEditorService } from './form-field-editor.service';
import { FormFieldComponent, MoveFieldDirection } from './form-field.component';
import {
  AvailableField,
  AvailableFields,
  checkInvalidForm,
  EditableFormField,
  FORM_FIELD_ZERO_VALUES,
  FormBuilderDependencies,
  FormBuilderLibraryInjectionToken,
  FormChanges,
} from '../../interface';
import { cloneDeep } from 'lodash';
import { FieldType, FormConfigFieldSchemaInterface, MappedField } from '@vendasta/forms_microservice';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { CountryCodeService } from '@vendasta/galaxy/utility/country-codes';

function createPresetField(field: AvailableField): EditableFormField {
  const mappedFieldSchema = field.formField as MappedField;

  return new EditableFormField({
    schema: {
      mappedField: mappedFieldSchema,
    },
    label: field.fieldName ?? '',
    required: mappedFieldSchema.systemDefined.required,
    hidden: mappedFieldSchema.systemDefined.hidden,
    preFillByUrlQueryParameter: mappedFieldSchema.id,
  });
}

function addPresetFieldsToForm(form: EditableFormField[], fieldGroups: AvailableFields[]): EditableFormField[] {
  if (!fieldGroups?.length) {
    return form;
  }
  const allFields = [];
  for (const group of fieldGroups) {
    allFields.push(...group.fields);
  }
  const includedByDefault = allFields.filter(
    (field) => (field.formField as MappedField)?.systemDefined?.includedByDefault,
  );
  const alreadyIncludedInForm = new Map<string, boolean>();
  for (const field of form) {
    if (!field.getFieldId()) {
      continue;
    }
    alreadyIncludedInForm.set(field.getFieldId(), true);
  }
  for (const field of includedByDefault) {
    if (alreadyIncludedInForm.get(field.formField?.id || '')) {
      continue;
    }
    form.push(createPresetField(field));
  }

  return form;
}

@Component({
  selector: 'form-builder',
  templateUrl: './form-editor.component.html',
  styleUrls: ['./form-editor.component.scss', './empty-form-hint.scss', '../../side-panels.scss'],
  imports: [
    AvailableFieldComponent,
    CommonModule,
    DragDropModule,
    FormFieldComponent,
    GalaxyAlertModule,
    GalaxyFormFieldModule,
    GalaxyWrapModule,
    MatButtonModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSidenavModule,
    ReactiveFormsModule,
    TranslateModule,
  ],
  providers: [CountryCodeService],
})
export class FormEditorComponent implements OnInit, OnDestroy, AfterViewInit, OnChanges {
  @ViewChild('formBuilderFieldEditorContainer', {
    read: ViewContainerRef,
  })
  formBuilderFieldEditorContainerRef: ViewContainerRef | undefined = undefined;
  formFieldEditorComponentRef: ComponentRef<FormFieldEditorComponent> | undefined = undefined;
  lastActiveFieldId: string | undefined = undefined; // field ID we had at the end of the last form-builder-field-editor recreation

  @Input() set existingFormToEdit(form: EditableFormField[]) {
    this.allFieldGroups$.pipe(take(1)).subscribe((fieldGroups) => {
      this.editableForm.set(addPresetFieldsToForm(form || [], fieldGroups));
      this.formLoaded$$.next();
    });
  }
  // see comment for FormFieldEditorComponent.isActive
  @Input() isActive = false;

  @Output() formChanged = new EventEmitter<FormChanges>();

  draggingOutsideFieldList = false;

  fieldTypes = AuxiliaryDataFieldType;
  searchControl = new FormControl<string>('', { nonNullable: true });
  searchText$: Observable<string> = this.searchControl.valueChanges.pipe(
    startWith(this.searchControl.value),
    map(() => this.searchControl.value), // I think there's a bug where .valueChanges emits an empty string when .value is still a real string
    debounceTime(250),
    shareReplay({ bufferSize: 1, refCount: true }),
  );
  private allFieldGroups$: Observable<AvailableFields[]> = this.listFields('').pipe(
    shareReplay({ bufferSize: 1, refCount: true }),
  );
  fieldGroups$: Observable<AvailableFields[]> = of([]);

  editableForm = signal<EditableFormField[]>([]);
  hasNoVisibleField = computed(() => {
    return this.editableForm().filter((field) => !field.isIncludedByDefault()).length === 0;
  });

  readonly formChanged$$ = new Subject<void>();
  form$: Observable<EditableFormField[]> = this.formChanged$$.pipe(
    startWith(null),
    map(() => this.editableForm()),
    filter((form) => !!form),
    shareReplay({ bufferSize: 1, refCount: true }),
  );
  private readonly formLoaded$$ = new Subject<void>();

  readonly showFieldEditor$: Observable<boolean> = this.formFieldEditorService.fieldOpenForEditing$.pipe(
    map((field) => !!field),
  );

  destroyed$$ = new Subject<void>();
  private readonly destroyRef = inject(DestroyRef);

  private readonly injector = inject(Injector);

  constructor(
    @Inject(FormBuilderLibraryInjectionToken) private readonly config: FormBuilderDependencies,
    private readonly formFieldEditorService: FormFieldEditorService,
    private readonly translate: TranslateService,
  ) {}

  ngOnInit(): void {
    this.fieldGroups$ = this.showFieldEditor$.pipe(
      switchMap(() => this.searchText$),
      switchMap((search) => this.listFields(search)),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

    this.formChanged$$.pipe(takeUntil(this.destroyed$$)).subscribe(() => this.emitFormChanges(true));

    this.formLoaded$$.pipe(takeUntil(this.destroyed$$)).subscribe(() => {
      this.checkEnforcedHidden();
      this.emitFormChanges(false);
      this.formFieldEditorService.cancelEdits();
    });

    this.destroyed$$.pipe(take(1)).subscribe(() => this.formFieldEditorService.cancelEdits());
  }

  ngAfterViewInit(): void {
    // Changing the active field in cause side-effects in the form-builder-field-editor (specifically, [(ngModel)] bindings).
    // We solve this by forcibly destroying and creating the form-builder-field-editor component when changing the active field
    // https://www.c-sharpcorner.com/article/how-to-create-and-destroy-dynamic-components-in-angular/
    combineLatest([this.formFieldEditorService.fieldOpenForEditing$, this.showFieldEditor$])
      .pipe(debounceTime(50))
      .subscribe(([fieldOpenForEditing, showFieldEditor]) => {
        // if the user has reactivated the same field, do nothing
        if (fieldOpenForEditing?.getFieldId() === this.lastActiveFieldId) {
          return;
        }

        this.destroyFormFieldEditor();

        // only recreate the component if it should actually be shown
        if (showFieldEditor) {
          this.createFormFieldEditor(fieldOpenForEditing);
        }

        // set the last active field ID so we won't reinitialize the form-builder-field-editor on the same field
        this.lastActiveFieldId = fieldOpenForEditing?.getFieldId() ?? '';
      });
  }

  createFormFieldEditor(fieldToEdit: EditableFormField | null): void {
    if (!this.formBuilderFieldEditorContainerRef) {
      return;
    }
    this.formFieldEditorComponentRef = this.formBuilderFieldEditorContainerRef.createComponent(
      FormFieldEditorComponent,
      {
        injector: Injector.create({
          providers: [CountryCodeService],
          parent: this.injector,
        }),
      },
    );
    this.formFieldEditorComponentRef.setInput('formChanges$$', this.formChanged$$);
    this.formFieldEditorComponentRef.setInput('isActive', false);
    this.formFieldEditorComponentRef.setInput('field', fieldToEdit);
    this.formFieldEditorComponentRef.instance.fieldChanged.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.formChanged$$.next();
    });
  }

  destroyFormFieldEditor(): void {
    // only destroy the component if it has been created
    if (this.formFieldEditorComponentRef !== undefined) {
      this.formFieldEditorComponentRef.destroy();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.formFieldEditorComponentRef !== undefined && changes['isActive']) {
      this.formFieldEditorComponentRef.setInput('isActive', changes['isActive'].currentValue);
    }
  }

  ngOnDestroy(): void {
    this.destroyed$$.next();
    this.destroyed$$.unsubscribe();
    this.destroyFormFieldEditor();
  }

  private emitFormChanges(changedByUser: boolean): void {
    this.formChanged.emit({
      changedByUser: changedByUser,
      fields: this.editableForm().map((f) => f.formField),
      invalidForm: checkInvalidForm(this.editableForm()),
    });
  }

  private prepareDataForDrop(data: AvailableField): FormConfigFieldSchemaInterface {
    if ('mappedTo' in data.formField) {
      return {
        mappedField: data.formField,
      };
    }
    return {
      unmappedField: {
        ...data.formField,
        id: `${data.formField.id}-${uuidv4()}`,
      },
    };
  }

  public checkMappedDuplicate(field: AvailableField): boolean {
    let repeated = false;
    if ('mappedTo' in field.formField) {
      for (let i = 0; i <= this.editableForm().length; i++) {
        if (field.formField.id == this.editableForm()[i]?.getFieldId()) {
          repeated = true;
          break;
        }
      }
    }
    return repeated;
  }

  onCdkDragEntered(event: CdkDragEnter<string[]>): void {
    this.draggingOutsideFieldList = event.container !== event.item.dropContainer;
  }

  onCdkDragEnded(): void {
    this.draggingOutsideFieldList = false;
  }

  dropIntoEditor(event: CdkDragDrop<string[]>): void {
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      const draggedField: AvailableField = event.item.data;
      const fieldSchema: FormConfigFieldSchemaInterface = this.prepareDataForDrop(draggedField);

      const formField: EditableFormField = new EditableFormField({
        schema: fieldSchema,
        label: FORM_FIELD_ZERO_VALUES.label || this.translate.instant(draggedField.fieldName || ''),
        // always copy a new object for default value or there will be reference issues in the editor down the line
        defaultValue: { ...FORM_FIELD_ZERO_VALUES.defaultValue },
        required: FORM_FIELD_ZERO_VALUES.required,
        hidden: FORM_FIELD_ZERO_VALUES.hidden || fieldSchema?.mappedField?.systemDefined?.hidden,
      });

      if (!this.checkMappedDuplicate(draggedField)) {
        const editableFormCopy = [...this.editableForm()];
        editableFormCopy.splice(event.currentIndex, 0, formField);
        this.editableForm.set(editableFormCopy);
      }
    }
    this.formChanged$$.next();
  }

  removeField(i: number): void {
    let removedField = undefined;
    if (this.editableForm().length >= i) {
      removedField = this.editableForm()[i];
    }
    this.formFieldEditorService
      .fieldIsCurrentlyOpenForEdits(removedField)
      .pipe(take(1))
      .subscribe((isSame) => {
        if (isSame) {
          this.formFieldEditorService.cancelEdits();
        }
      });
    const editableFormCopy = [...this.editableForm()];
    editableFormCopy.splice(i, 1);
    this.editableForm.set(editableFormCopy);
    this.formChanged$$.next();
  }

  duplicateField(field: EditableFormField, index: number): void {
    const duplicatedField = cloneDeep(field);
    duplicatedField.setFieldId(`${uuidv4()}`);
    const editableFormCopy = [...this.editableForm()];
    editableFormCopy.splice(index, 0, duplicatedField);
    this.editableForm.set(editableFormCopy);
    this.formChanged$$.next();
  }

  isSection(field: EditableFormField): boolean {
    return field?.formField?.schema?.unmappedField?.type == FieldType.FIELD_TYPE_SECTION;
  }

  addSection(): void {
    console.log('Adding a new Section');
    const formField: EditableFormField = new EditableFormField({
      schema: {
        unmappedField: {
          id: `Section-${uuidv4()}`,
          type: FieldType.FIELD_TYPE_SECTION,
        },
      },
      label: FORM_FIELD_ZERO_VALUES.label || this.translate.instant('GENERIC_FIELD_TYPES.SECTION'),
      // always copy a new object for default value or there will be reference issues in the editor down the line
      defaultValue: { ...FORM_FIELD_ZERO_VALUES.defaultValue },
      required: FORM_FIELD_ZERO_VALUES.required,
      hidden: FORM_FIELD_ZERO_VALUES.hidden,
    });
    const editableFormCopy = [...this.editableForm()];
    editableFormCopy.splice(this.findLastVisiblePosition(), 0, formField);
    this.editableForm.set(editableFormCopy);
    this.formChanged$$.next();
  }

  private findLastVisiblePosition(): number {
    let total = 0;
    for (let i = 0; i < this.editableForm().length; i++) {
      if (!this.editableForm()[i].isHiddenBySystem()) {
        total++;
      }
    }
    return total;
  }

  private moveFieldIndex(index: number, direction: MoveFieldDirection): number {
    switch (direction) {
      case MoveFieldDirection.MoveToTop:
        return 0;
      case MoveFieldDirection.MoveToBottom:
        return this.editableForm().length - 1;
      case MoveFieldDirection.MoveDown:
        if (index + 1 > this.editableForm().length - 1) {
          return this.editableForm().length - 1;
        }
        return index + 1;
      case MoveFieldDirection.MoveUp:
        if (index - 1 < 0) {
          return 0;
        }
        return index - 1;
      default:
        return 0;
    }
  }

  moveField(direction: MoveFieldDirection, fromIndex: number): void {
    const toIndex = this.moveFieldIndex(fromIndex, direction);
    const movedField = this.editableForm()[fromIndex];
    const editableFormCopy = [...this.editableForm()];
    editableFormCopy.splice(fromIndex, 1);
    editableFormCopy.splice(toIndex, 0, movedField);
    this.editableForm.set(editableFormCopy);
    this.formChanged$$.next();
  }

  private listFields(search: string): Observable<AvailableFields[]> {
    return this.config.listFields(search, '', 100).pipe(
      map((availableFields) => {
        const noEntriesFound = availableFields.every((group) => group.fields?.length === 0);
        if (noEntriesFound) {
          return [];
        }
        return availableFields;
      }),
      catchError(() => {
        console.warn('failed to list custom fields');
        return EMPTY;
      }),
      take(1),
    );
  }

  checkEnforcedHidden() {
    this.editableForm().map((f) => {
      if (f.formField && !f.formField.hidden && f.formField.schema?.mappedField?.systemDefined?.hidden) {
        f.formField.hidden = true;
      }
    });
  }

  sectionEnabled(): Observable<boolean> {
    return this.config.hasSections$;
  }
}
