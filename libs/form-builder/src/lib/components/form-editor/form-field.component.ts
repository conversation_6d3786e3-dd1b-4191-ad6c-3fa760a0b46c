import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { FormFieldEditorService } from './form-field-editor.service';
import { EditableFormField, FORM_FIELD_ZERO_VALUES, getMaterialIconKey, getLabelTranslationKey } from '../../interface';
import { TranslationModule } from '../../translation-module';
import { MatMenuModule } from '@angular/material/menu';
import { FieldType } from '@vendasta/forms_microservice';

export enum MoveFieldDirection {
  MoveToTop = 1,
  MoveUp,
  MoveDown,
  MoveToBottom,
}

@Component({
  selector: 'form-builder-field',
  template: `
    <mat-card
      appearance="outlined"
      class="field-container"
      [ngClass]="{
        'field-selected': (fieldIsSelected$ | async),
        'offset-section-card': isSection(),
      }"
      (click)="openFieldEditor()"
    >
      <mat-icon class="drag-field-icon" aria-hidden="false" *ngIf="!isSection()">drag_indicator</mat-icon>
      <mat-icon class="field-icon" aria-hidden="false">{{ icon }}</mat-icon>
      <div class="field-name">{{ label | translate }}</div>
      <div class="space"></div>
      <button
        mat-icon-button
        aria-label="field options"
        [matMenuTriggerFor]="fieldOptions"
        class="options-button"
        data-cy="form-builder-item-menu-button"
        *ngIf="!isSection()"
      >
        <mat-icon class="field-options-icon" aria-hidden="false">more_horiz</mat-icon>
      </button>
      <mat-menu #fieldOptions="matMenu">
        <button
          *ngIf="canDuplicate()"
          mat-menu-item
          (click)="duplicateField.emit()"
          data-cy="form-builder-duplicate-menu-item"
        >
          <mat-icon class="field-options-icon">content_copy</mat-icon>
          {{ 'FIELD_OPERATIONS.DUPLICATE' | translate }}
        </button>
        <button mat-menu-item [matMenuTriggerFor]="order" data-cy="form-builder-order-menu-item">
          <mat-icon class="field-options-icon">import_export</mat-icon>
          {{ 'FIELD_OPERATIONS.ORDER' | translate }}
        </button>
      </mat-menu>
      <mat-menu #order="matMenu">
        <button
          mat-menu-item
          (click)="moveField.emit(MoveFieldDirection.MoveToTop)"
          data-cy="form-builder-order-to-top-menu-item"
        >
          {{ 'FIELD_OPERATIONS.ORDER_SUB_OPTIONS.MOVE_TO_TOP' | translate }}
        </button>
        <button
          mat-menu-item
          (click)="moveField.emit(MoveFieldDirection.MoveUp)"
          data-cy="form-builder-order-move-up-menu-item"
        >
          {{ 'FIELD_OPERATIONS.ORDER_SUB_OPTIONS.MOVE_UP' | translate }}
        </button>
        <button
          mat-menu-item
          (click)="moveField.emit(MoveFieldDirection.MoveDown)"
          data-cy="form-builder-order-move-down-menu-item"
        >
          {{ 'FIELD_OPERATIONS.ORDER_SUB_OPTIONS.MOVE_DOWN' | translate }}
        </button>
        <button
          mat-menu-item
          (click)="moveField.emit(MoveFieldDirection.MoveToBottom)"
          data-cy="form-builder-order-to-bottom-menu-item"
        >
          {{ 'FIELD_OPERATIONS.ORDER_SUB_OPTIONS.MOVE_TO_BOTTOM' | translate }}
        </button>
      </mat-menu>
      <button
        mat-icon-button
        aria-label="remove form field"
        (click)="removeField.emit()"
        data-cy="form-builder-remove-button"
      >
        <mat-icon class="remove-field-icon" aria-hidden="false">close</mat-icon>
      </button>
    </mat-card>
  `,
  styleUrls: ['./field-colors.scss'],
  styles: [
    `
      mat-card {
        display: flex;
        flex-direction: row;
        align-items: center;
      }

      .field-name {
        font-weight: 500;
        overflow: auto;
        overflow-wrap: break-word;
      }

      .space {
        flex-grow: 42;
      }

      mat-card > *:not(:last-child) {
        margin-right: 8px;
      }

      mat-card > .options-button {
        margin-right: 2px;
      }

      mat-card.offset-section-card {
        margin-left: -20px;
        cursor: default;
        padding-left: 5px;
      }
    `,
  ],
  imports: [
    CommonModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    ReactiveFormsModule,
    TranslateModule,
    TranslationModule,
    MatMenuModule,
  ],
  standalone: true,
})
export class FormFieldComponent implements OnInit, OnDestroy {
  @Input() editableFormField = new EditableFormField(FORM_FIELD_ZERO_VALUES);
  @Input() fieldChanges$$ = new Subject<void>();
  @Output() removeField = new EventEmitter<void>();
  @Output() duplicateField = new EventEmitter<void>();
  @Output() moveField = new EventEmitter<MoveFieldDirection>();

  MoveFieldDirection = MoveFieldDirection;
  icon = '';
  label = '';
  fieldType: FieldType = FieldType.FIELD_TYPE_INVALID;
  fieldTypes = FieldType;
  fieldIsSelected$ = this.formFieldEditorService.fieldOpenForEditing$.pipe(
    map((field) => field?.getFieldId() === this.editableFormField?.getFieldId()),
  );

  private readonly destroyed$$ = new Subject<void>();

  constructor(private readonly formFieldEditorService: FormFieldEditorService) {}

  ngOnInit(): void {
    this.update();
    this.fieldChanges$$.pipe(takeUntil(this.destroyed$$)).subscribe(() => this.update());
  }

  canDuplicate(): boolean {
    return this.editableFormField?.formField?.schema?.mappedField?.mappedTo == undefined;
  }

  ngOnDestroy(): void {
    this.destroyed$$.next();
  }

  openFieldEditor(): void {
    this.formFieldEditorService.changeToField(this.editableFormField);
  }

  isSection(): boolean {
    return this.editableFormField?.formField?.schema?.unmappedField?.type == FieldType.FIELD_TYPE_SECTION;
  }

  private update(): void {
    this.fieldType = this.editableFormField.getFieldType();
    this.icon = getMaterialIconKey(this.fieldType);

    // label stores HTML for Rich Text Element, which we don't want to display, so display the field type label instead
    if (this.fieldType === this.fieldTypes.FIELD_TYPE_RICH_TEXT_ELEMENT) {
      this.label = getLabelTranslationKey(this.fieldType);
    } else {
      this.label = this.editableFormField.formField?.label || '';
    }
  }
}
