<mat-drawer-container autosize="true" cdkDropListGroup class="editor-container">
  <mat-drawer class="palette" mode="side" opened disableClose="true">
    <div class="palette-container">
      <div class="field-list-search">
        <glxy-form-field [bottomSpacing]="false" suffixIcon="search" [showLabel]="false">
          <glxy-label>Search</glxy-label>
          <input type="text" placeholder="{{ 'SEARCH_FIELDS' | translate }}" matInput [formControl]="searchControl" />
        </glxy-form-field>
      </div>
      <ng-container *ngIf="fieldGroups$ | async as fieldGroups">
        <mat-accordion [displayMode]="'flat'" [multi]="true" *ngIf="fieldGroups?.length > 0">
          <ng-container *ngFor="let fieldGroup of fieldGroups">
            <mat-expansion-panel *ngIf="!fieldGroup?.hiddenGroup" class="field-group" [expanded]="true">
              <mat-expansion-panel-header
                class="field-group__label"
                [collapsedHeight]="'fit-content'"
                [expandedHeight]="'fit-content'"
              >
                <mat-panel-title>
                  {{ fieldGroup.labelKey | translate }}
                </mat-panel-title>
              </mat-expansion-panel-header>
              <cdk-drop-list
                [cdkDropListSortingDisabled]="true"
                [cdkDropListData]="fieldGroup.fields"
                [cdkDropListConnectedTo]="[editableFormListOfFields]"
              >
                <div
                  *ngFor="let field of fieldGroup.fields"
                  cdkDrag
                  (cdkDragEntered)="onCdkDragEntered($event)"
                  (cdkDragEnded)="onCdkDragEnded()"
                  [cdkDragData]="field"
                  [cdkDragDisabled]="checkMappedDuplicate(field)"
                >
                  <ng-template #availableFieldElement>
                    <form-builder-available-field
                      *ngIf="!field.formField?.systemDefined?.includedByDefault"
                      class="available-field"
                      [fieldName]="field.fieldName"
                      [fieldType]="field.formField.type"
                      [fieldId]="field.formField.id"
                      [required]="field.formField.systemDefined?.required"
                      [hidden]="field.formField.systemDefined?.hidden"
                    ></form-builder-available-field>
                  </ng-template>
                  <div *cdkDragPlaceholder>
                    <div
                      class="draggable-field__placeholder"
                      *ngIf="draggingOutsideFieldList; else availableFieldElement"
                    ></div>
                  </div>
                  <ng-container *ngTemplateOutlet="availableFieldElement"></ng-container>
                </div>
              </cdk-drop-list>
            </mat-expansion-panel>
          </ng-container>
        </mat-accordion>
        <div *ngIf="fieldGroups?.length === 0" class="empty-search-results">
          <p>{{ 'FIELDS_NOT_FOUND' | translate }}</p>
        </div>
      </ng-container>
    </div>
  </mat-drawer>
  <mat-drawer position="end" opened mode="side" disableClose="true">
    <!-- contains dynamically created form-builder-field-editor, which is recreated whenever the actively edited field changes -->
    <ng-template #formBuilderFieldEditorContainer></ng-template>
    <ng-container *ngIf="(showFieldEditor$ | async) === false">
      <div class="field-editor-placeholder">
        {{ 'FIELD_OPTIONS.PLACEHOLDER_MESSAGE' | translate }}
      </div>
    </ng-container>
  </mat-drawer>

  <mat-drawer-content>
    <div class="canvas">
      <cdk-drop-list
        class="editable-form"
        [cdkDropListData]="editableForm()"
        (cdkDropListDropped)="dropIntoEditor($event)"
        #editableFormListOfFields="cdkDropList"
        data-cy="editable-form-drop-list"
      >
        <glxy-alert
          class="empty-form-hint"
          type="tip"
          *ngIf="hasNoVisibleField(); else moreThanZeroFields"
          [innerHtml]="'EMPTY_FORM_HINT' | translate"
        ></glxy-alert>
        <ng-template #moreThanZeroFields>
          <ng-container *ngFor="let field of form$ | async; let i = index">
            <form-builder-field
              *ngIf="!field.isIncludedByDefault()"
              cdkDrag
              [cdkDragDisabled]="isSection(field)"
              [editableFormField]="field"
              [fieldChanges$$]="formChanged$$"
              (removeField)="removeField(i)"
              (duplicateField)="duplicateField(field, i)"
              (moveField)="moveField($event, i)"
            >
              <div class="draggable-field__placeholder" *cdkDragPlaceholder></div>
            </form-builder-field>
          </ng-container>
        </ng-template>
      </cdk-drop-list>
      <span class="add-section-bar" (click)="addSection()" *ngIf="sectionEnabled() | async">
        <mat-icon class="field-icon" aria-hidden="false">add</mat-icon>
        {{ 'ACTIONS.ADD_SECTION' | translate }}
      </span>
      <span class="submit-button-container" *ngIf="(sectionEnabled() | async) === false">
        <button class="submit-button" *ngIf="!hasNoVisibleField()" mat-flat-button color="primary">
          {{ 'ACTIONS.SUBMIT' | translate }}
        </button>
      </span>
    </div>
  </mat-drawer-content>
</mat-drawer-container>
