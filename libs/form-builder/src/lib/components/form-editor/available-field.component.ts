import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { FieldType } from '@vendasta/forms_microservice';
import { getMaterialIconKey } from '../../interface';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'form-builder-available-field',
  template: `
    <mat-card appearance="outlined" class="field" [attr.data-cy]="'available-field-' + fieldId">
      <mat-icon class="drag-field-icon" aria-hidden="false">drag_indicator</mat-icon>
      <mat-icon class="available-field-icon" aria-hidden="false">{{ icon }}</mat-icon>
      <div class="available-field-name">{{ fieldName }}</div>
      <div class="right-badge">
        <glxy-badge *ngIf="required" [color]="'grey'" [size]="'small'" class="right-align">
          {{ 'CUSTOM_FORMS.LIST_OF_FIELDS.REQUIRED' | translate }}
        </glxy-badge>
      </div>
    </mat-card>
  `,
  styleUrls: ['./field-colors.scss'],
  styles: [
    `
      mat-card {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 100%;
        padding-left: 6px;
      }

      mat-card > * {
        margin-right: 8px;
      }

      .field {
        border-color: var(--drag-border-color);
        color: var(--drag-color);
        background-color: var(--drag-background-color);
        cursor: var(--drag-cursor);
      }

      .right-badge {
        flex-grow: 10;
        display: flex;
        flex-direction: column;
      }

      .right-align {
        align-self: flex-end;
      }
    `,
  ],
  imports: [CommonModule, MatCardModule, MatIconModule, GalaxyBadgeModule, TranslateModule],
  standalone: true,
})
export class AvailableFieldComponent implements OnInit {
  @Input() fieldName = '';
  @Input() fieldType = FieldType.FIELD_TYPE_INVALID;
  @Input() fieldId = '';
  @Input() required = false;
  @Input() hidden = false;
  icon = '';

  ngOnInit(): void {
    this.icon = getMaterialIconKey(this.fieldType);
  }
}
