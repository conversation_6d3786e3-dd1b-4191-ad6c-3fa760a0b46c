import {
  FieldOptionInterface,
  FieldType,
  FormConfigFieldInterface as Form<PERSON>ield,
  JsonSchemaLibrary,
  MappedFieldInterface as Mapped<PERSON>ield,
  StylesInterface,
  UnmappedFieldInterface as UnmappedField,
} from '@vendasta/forms_microservice';
import { Observable } from 'rxjs';
import { InjectionToken } from '@angular/core';

export interface FormChanges {
  fields: FormField[];
  changedByUser: boolean;
  invalidForm: boolean;
}

export interface FormBuilderDependencies {
  namespace$: Observable<string>;
  schemaLibrary: JsonSchemaLibrary;
  listFields(search: string, cursor: string, pageSize: number): Observable<AvailableFields[]>;
  previewForm(formFields: FormField[], submitButtonLabel?: string): Observable<FormPreview>;
  routePrefix$: Observable<string>;
  parentNamespace$: Observable<string>;
  hasSections$: Observable<boolean>;
}
export const FormBuilderLibraryInjectionToken = new InjectionToken<FormBuilderDependencies>(
  'dependencies given to form builder',
);

export interface FormPreview {
  jsonSchema: string;
  jsonUiSchema: string;
}

export type AvailableFormField = UnmappedField | MappedField;

export interface AvailableField {
  fieldName?: string;
  formField: AvailableFormField;
}

export interface AvailableFields {
  labelKey: string;
  fields: AvailableField[];
  hiddenGroup?: boolean;
}

export const AVAILABLE_FIELD_ZERO_VALUES: AvailableField = {
  formField: {
    id: '',
    type: FieldType.FIELD_TYPE_INVALID,
  },
  fieldName: '',
};

export const FORM_FIELD_ZERO_VALUES: FormField = {
  schema: {
    unmappedField: {
      ...AVAILABLE_FIELD_ZERO_VALUES.formField,
    },
  },
  label: '',
  required: false,
  hidden: false,
  defaultValue: {
    invalid: true,
  },
};

export type DefaultValue = string | number | Date | boolean | string[] | null | undefined;

export class EditableFormField {
  formField: FormField = {
    schema: {
      unmappedField: {
        ...AVAILABLE_FIELD_ZERO_VALUES.formField,
      },
      mappedField: {
        ...AVAILABLE_FIELD_ZERO_VALUES.formField,
      },
    },
  };
  private _populateFieldDynamically = false;

  constructor(formField: FormField) {
    this.formField = formField;
    if (!this.formField.defaultValue) {
      this.formField.defaultValue = {};
    }
    if (this.formField?.preFillByUrlQueryParameter && this.formField?.preFillByUrlQueryParameter?.length > 0) {
      this.populateFieldDynamically = true;
    }
  }

  get defaultValue(): DefaultValue {
    switch (this.getFieldType()) {
      case FieldType.FIELD_TYPE_STRING:
      case FieldType.FIELD_TYPE_TEXT_AREA:
      case FieldType.FIELD_TYPE_DATE:
      case FieldType.FIELD_TYPE_RADIO:
      case FieldType.FIELD_TYPE_DROPDOWN:
        return this.formField?.defaultValue?.string;
      case FieldType.FIELD_TYPE_INTEGER:
      case FieldType.FIELD_TYPE_CURRENCY:
        return this.formField?.defaultValue?.integer;
      case FieldType.FIELD_TYPE_BOOLEAN:
        return this.formField?.defaultValue?.boolean;
      case FieldType.FIELD_TYPE_TAG:
        return this.formField?.defaultValue?.stringValues?.values;
      default:
        return null;
    }
  }

  set defaultValue(value: DefaultValue) {
    switch (this.getFieldType()) {
      case FieldType.FIELD_TYPE_STRING:
      case FieldType.FIELD_TYPE_TEXT_AREA:
      case FieldType.FIELD_TYPE_DATE:
      case FieldType.FIELD_TYPE_RADIO:
      case FieldType.FIELD_TYPE_DROPDOWN:
        if (value) {
          this.formField.defaultValue = { string: value as string };
        } else {
          this.formField.defaultValue = { string: '' };
        }
        break;
      case FieldType.FIELD_TYPE_INTEGER:
      case FieldType.FIELD_TYPE_CURRENCY:
        if (Number.parseInt((value as string) || '0', 10)) {
          this.formField.defaultValue = { integer: Number.parseInt((value as string) || '0', 10) };
        } else {
          this.formField.defaultValue = { integer: 0 };
        }
        break;
      case FieldType.FIELD_TYPE_BOOLEAN:
        this.formField.defaultValue = { boolean: !!value };
        break;
      case FieldType.FIELD_TYPE_TAG:
        this.formField.defaultValue = { stringValues: { values: value as string[] } };
    }
  }

  public get options(): FieldOptionInterface[] {
    return this.formField.schema?.unmappedField?.options || this.formField.schema?.mappedField?.options || [];
  }

  public set options(opts: FieldOptionInterface[]) {
    if (this.formField.schema?.unmappedField) {
      this.formField.schema.unmappedField.options = opts;
    } else if (this.formField.schema?.mappedField) {
      this.formField.schema.mappedField.options = opts;
    }
  }

  setFieldId(id: string): void {
    if (!id) {
      return;
    }
    if (this.formField.schema?.unmappedField) {
      this.formField.schema.unmappedField.id = id;
    } else if (this.formField.schema?.mappedField) {
      this.formField.schema.mappedField.id = id;
    }
  }

  getFieldId(): string {
    return this.formField?.schema?.unmappedField?.id || this.formField?.schema?.mappedField?.id || '';
  }

  getFieldType(): FieldType {
    if (this.formField?.schema?.unmappedField?.type) {
      return this.formField?.schema?.unmappedField?.type;
    } else if (this.formField?.schema?.mappedField?.type) {
      return this.formField?.schema?.mappedField?.type;
    }
    return FieldType.FIELD_TYPE_INVALID;
  }

  getCurrencyCode(): string {
    return this.formField?.schema?.unmappedField?.currencyCode || '';
  }

  private isDefaultFieldRequired(): boolean {
    return (
      !!this.formField?.hidden && !!this.formField?.required && this.getFieldType() !== FieldType.FIELD_TYPE_BOOLEAN
    );
  }

  get populateFieldDynamically(): boolean {
    return this._populateFieldDynamically;
  }

  set populateFieldDynamically(prepopulate: boolean) {
    this._populateFieldDynamically = prepopulate;
    if (!prepopulate) {
      this.formField.preFillByUrlQueryParameter = '';
    }
  }

  fieldsToValidate(): Map<string, boolean> {
    return new Map<string, boolean>([
      [labelProperty, true],
      [defaultValueProperty, this.isDefaultFieldRequired()],
    ]);
  }

  fieldValidationErrors(): Map<string, string> {
    const errorMap = new Map();
    if (this.isDefaultFieldRequired() && !this.defaultValue) {
      errorMap.set(defaultValueProperty, 'FIELD_OPTIONS.ERRORS.REQUIRED_DEFAULT');
    }
    if (!this.formField?.label) {
      errorMap.set(labelProperty, 'FIELD_OPTIONS.ERRORS.REQUIRED_LABEL');
    }
    const queryParam = this.formField?.preFillByUrlQueryParameter || '';
    if (this.populateFieldDynamically && queryParam.length < 1) {
      errorMap.set(queryParamInvalid, 'FIELD_OPTIONS.ERRORS.REQUIRED_QUERY_PARAM');
    }
    return errorMap;
  }

  isIncludedByDefault(): boolean {
    return !!this.formField?.schema?.mappedField?.systemDefined?.includedByDefault;
  }

  isHiddenBySystem(): boolean {
    return !!this.formField?.schema?.mappedField?.systemDefined?.hidden;
  }
}
export const labelProperty = 'label';
export const defaultValueProperty = 'defaultValue';
export const queryParamInvalid = 'queryParamInvalid';

export function checkInvalidForm(form: EditableFormField[]): boolean {
  if (form.length < 1) {
    return true;
  }
  for (const field of form) {
    if (field.fieldValidationErrors().size > 0) {
      return true;
    }
  }
  return false;
}

export interface StyleChanges {
  changedByUser: boolean;
  pending?: boolean;
  invalid?: boolean;
  styles?: StylesInterface;
}

interface FieldTypeConfig {
  labelTranslationKey: string;
  materialIconKey: string;
}

const UnknownLabelTranslation = 'COMMON.UNKNOWN';
const UnknownMaterialIcon = 'not_listed_location';
const FieldTypeConfigMap: Map<FieldType, FieldTypeConfig> = new Map<FieldType, FieldTypeConfig>([
  [
    FieldType.FIELD_TYPE_STRING,
    {
      labelTranslationKey: 'GENERIC_FIELD_TYPES.STRING',
      materialIconKey: 'text_fields',
    },
  ],
  [
    FieldType.FIELD_TYPE_INTEGER,
    {
      labelTranslationKey: 'GENERIC_FIELD_TYPES.INTEGER',
      materialIconKey: '123',
    },
  ],
  [
    FieldType.FIELD_TYPE_DROPDOWN,
    {
      labelTranslationKey: 'GENERIC_FIELD_TYPES.DROPDOWN',
      materialIconKey: 'keyboard_arrow_down',
    },
  ],
  [
    FieldType.FIELD_TYPE_BOOLEAN,
    {
      labelTranslationKey: 'GENERIC_FIELD_TYPES.BOOLEAN',
      materialIconKey: 'check_box',
    },
  ],
  [
    FieldType.FIELD_TYPE_DATE,
    {
      labelTranslationKey: 'GENERIC_FIELD_TYPES.DATE',
      materialIconKey: 'event',
    },
  ],
  [
    FieldType.FIELD_TYPE_CURRENCY,
    {
      labelTranslationKey: 'GENERIC_FIELD_TYPES.CURRENCY',
      materialIconKey: 'savings',
    },
  ],
  [
    FieldType.FIELD_TYPE_RADIO,
    {
      labelTranslationKey: 'GENERIC_FIELD_TYPES.RADIO',
      materialIconKey: 'radio_button_checked',
    },
  ],
  [
    FieldType.FIELD_TYPE_PHONE,
    {
      labelTranslationKey: 'GENERIC_FIELD_TYPES.PHONE',
      materialIconKey: 'phone',
    },
  ],
  [
    FieldType.FIELD_TYPE_RICH_TEXT_ELEMENT,
    {
      labelTranslationKey: 'ELEMENT_FIELD_TYPES.RICH_TEXT',
      materialIconKey: 'subject',
    },
  ],
  [
    FieldType.FIELD_TYPE_EMAIL,
    {
      labelTranslationKey: 'GENERIC_FIELD_TYPES.EMAIL',
      materialIconKey: 'mail',
    },
  ],
  [
    FieldType.FIELD_TYPE_TEXT_AREA,
    {
      labelTranslationKey: 'GENERIC_FIELD_TYPES.TEXT_AREA',
      materialIconKey: 'edit_note',
    },
  ],
  [
    FieldType.FIELD_TYPE_TAG,
    {
      labelTranslationKey: 'GENERIC_FIELD_TYPES.TAG',
      materialIconKey: 'sell',
    },
  ],
  [
    FieldType.FIELD_TYPE_BUSINESS_SEARCH,
    {
      labelTranslationKey: 'CRM_COMPANY_FIELD_TYPES.BUSINESS_SEARCH',
      materialIconKey: 'business',
    },
  ],
  [
    FieldType.FIELD_TYPE_SECTION,
    {
      labelTranslationKey: 'GENERIC_FIELD_TYPES.SECTION',
      materialIconKey: 'grid_view',
    },
  ],
  [
    FieldType.FIELD_TYPE_INVALID,
    {
      labelTranslationKey: UnknownLabelTranslation,
      materialIconKey: UnknownMaterialIcon,
    },
  ],
]);

export function getMaterialIconKey(fieldType: FieldType): string {
  return FieldTypeConfigMap.get(fieldType)?.materialIconKey || UnknownMaterialIcon;
}

export function getLabelTranslationKey(fieldType: FieldType): string {
  return FieldTypeConfigMap.get(fieldType)?.labelTranslationKey || UnknownLabelTranslation;
}
