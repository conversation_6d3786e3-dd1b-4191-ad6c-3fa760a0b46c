{"ACTIONS": {"SAVE": "Save", "SUBMIT": "Submit", "CANCEL": "Cancel", "CREATE": "Create", "DELETE": "Delete", "EDIT": "Edit", "ADD_SECTION": "Add new section"}, "EMPTY_FORM_HINT": "<strong>Drag a form field</strong> on your left to start building your form", "EMPTY_FORM_PREVIEW_HINT": "<strong>Click the build tab</strong> to start building your form", "SEARCH_FIELDS": "Search for a field", "FIELDS_NOT_FOUND": "No results found", "ELEMENT_FIELD_TYPES": {"RICH_TEXT": "Paragraph"}, "GENERIC_FIELD_TYPES": {"UNKNOWN": "Unknown", "STRING": "Text", "INTEGER": "Integer", "DATE": "Date", "DROPDOWN": "Dropdown", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "BOOLEAN": "Boolean", "RADIO": "Radio button", "EMAIL": "Email", "PHONE": "Phone number", "TEXT_AREA": "Long text", "TAG": "Tag", "SECTION": "Section"}, "CRM_COMPANY_FIELD_TYPES": {"BUSINESS_SEARCH": "Business search", "BUSINESS_SEARCH_HINT": "Search for an existing business to automatically fill other company information in this form."}, "FIELD_OPTIONS": {"LABEL": "Label", "VALUE": "Value", "OPTIONS": "Options", "DEFAULT": "Default value", "POPULATE_DYNAMICALLY": {"CHECKMARK": "Populate field dynamically", "QUERY_PARAM": "Query parameter", "QUERY_PARAM_HINT": "If a value is defined, default value will be overridden"}, "CHECKBOX_DEFAULT_VALUE": {"NOT_SELECTED": "Not selected", "SELECTED": "Selected"}, "REQUIRED": "Make required", "HIDDEN": "Make hidden", "NO_DEFAULT_OPTION": "No default option", "ERRORS": {"REQUIRED_LABEL": "Label is required", "REQUIRED_DEFAULT": "Default value is required if field is marked required", "REQUIRED_QUERY_PARAM": "Query parameter is required"}, "PLACEHOLDER_MESSAGE": "Select a form field to edit its properties", "FIELD_TYPE": "Field type", "PLACEHOLDER_TEXT": "Placeholder text", "PHONE_COUNTRY_CODE": "Default country code"}, "FIELD_OPERATIONS": {"DUPLICATE": "Duplicate", "ORDER": "Order", "ORDER_SUB_OPTIONS": {"MOVE_TO_TOP": "Move to top", "MOVE_UP": "Move up", "MOVE_DOWN": "Move down", "MOVE_TO_BOTTOM": "Move to bottom"}}, "FORM_STYLES": {"CONTAINER": {"GROUP_LABEL": "Container", "CONTAINER_WIDTH": "Container width", "BACKGROUND_COLOR": "Background color", "BORDER_COLOR": "Border color", "BORDER_WIDTH": "Border width"}, "FONT": {"GROUP_LABEL": "Font", "PRIMARY_COLOR": "Primary color"}, "SUBMIT_BUTTON": {"GROUP_LABEL": "<PERSON><PERSON>", "BUTTON_LABEL": "Button text", "COLOR": "Color", "FONT_COLOR": "Font color"}}, "CUSTOM_FORMS": {"PAGE_TITLE": "Custom forms", "MOBILE_WARNING": "Custom forms are not optimized for mobile devices, please use a desktop browser to create or edit custom forms.", "CREATE": {"PAGE_TITLE": "Create form", "DIALOG_TITLE": "Create new form", "UNTITLED_FORM_NAME": "New form", "FORM_NAME": "Form name", "LIST_OF_FIELDS": "Fields"}, "EDIT": {"PAGE_TITLE": "Edit form: {{name}}", "FORM_NAME": "Form name", "LIST_OF_FIELDS": "Fields", "SAVE_TOO_FAST": "Please retry saving the form.", "CANT_SAVE": "Fix the highlighted problems.", "SAVE_DIALOG_TITLE": "Save form", "SAVE_DIALOG_DESCRIPTION": "Changes were not saved, do you want to save the form ?", "SAVE_FORM": "Save form"}, "SECTIONS": {"BUILD": "Build", "DESIGN": "Design", "SETTINGS": "Settings"}, "LIST_OF_FIELDS": {"GENERIC": "Generic fields", "ELEMENT": "Element", "FIELD_TYPES": {"TEXT": "Text", "NUMBER": "Number", "DROPDOWN": "Dropdown", "RADIO_BUTTON": "Radio button", "CHECKBOX": "Checkbox", "DATE": "Date", "RICH_TEXT_ELEMENT": "Paragraph", "EMAIL": "Email", "PHONE": "Phone number"}, "REQUIRED": "Required"}, "CONFIGURE_FORM": {"EDIT_NAME": "Name", "SUBMISSION_SETTINGS": "Submission settings", "INBOX_CREATE": "Create a conversation in Inbox", "INBOX_CREATE_DESCRIPTION": "When a form is submitted, it automatically starts a new conversation in Inbox, or adds to an existing one if they've contacted you before.", "URL_REDIRECT": "Redirect link", "URL_REDIRECT_HINT": "Users will be sent here after submitting your form", "RECAPTCHA_SETTINGS": "reCAPTCHA settings", "RECAPTCHA_SETTINGS_HINT": "Set up Google reCAPTCHA to protect against bots and malicious activities.", "RECAPTCHA_SETTINGS_HINT_LINK": "Find site key and secret key", "RECAPTCHA_SITE_KEY": "Site key", "RECAPTCHA_SITE_KEY_ERROR": "Invalid site key", "RECAPTCHA_SECRET_KEY": "Secret key", "RECAPTCHA_SECRET_KEY_ERROR": "Invalid secret key"}, "LIST": {"PAGE_TITLE": "Forms", "CREATE_FORM_BUTTON": "Create form", "NAME": "Name", "SUBMISSIONS": "Submissions", "LAST_MODIFIED": "Last Modified", "EMPTY_MESSAGE": "No Forms found", "EMBED_FORM": "Embed form", "EMBED_FORM_DESCRIPTION": "Copy this code and paste it into the HTML of your website to embed this form.", "EMBED_CODE": "Embed code", "COPY_CODE": "Copy code", "DUPLICATE": "Duplicate", "EMBED": "Embed", "VIEW_SUBMISSIONS": "View submissions", "EXPORT_AS_CSV": "Export as CSV", "CREATED": "Created"}, "FORM_SUBMISSION_LIST": {"PAGE_HEADER_TITLE": "Submissions: {{formName}}", "EXPORT_TO_CSV": "Export as CSV", "EXPORT_TO_CSV_MESSAGES": {"STARTED": "Started download for CSV export", "FAILED": "Export CSV failed", "SUCCESS": "\"{{formName}}\" exported successfully!"}, "EDIT": "Edit form", "ERROR_LOADING_FORM": "Could not load form from submissions", "ERROR_LIST_SUBMISSION": "Could not load submissions", "ACTIONS": "Actions", "SUBMITTED_ON": "Submitted on"}, "SUCCESS_SAVING_FORM": "Form saved", "ERROR_SAVING_FORM": "Error saving the form", "DELETE_DIALOG": {"TITLE": "Delete form?", "DESCRIPTION": "Users who submit this form after it is deleted will receive an error. Consider replacing embedded versions of this form before deleting it."}, "SUCCESS_DELETING_FORM": "Form Deleted", "ERROR_DELETING_FORM": "Error deleting form", "ERROR_EMBED_NEW_FORM": "Must save form before generating embedded code"}, "COMMON": {"TABLE": {"OF": "of", "OF_MANY": "of many", "NO_RESULTS": "No results found for filters", "SHOWING_PAGE_OF_TOTAL": "Showing {{page}} of {{total}}", "TABLE_SHOWING": "Showing {{page}} of {{total}} results", "TABLE_SHOWING_OVER_RESULT": "Showing {{page}} of over {{total}} results", "TABLE_SHOWING_ALL_RESULT": "Showing all {{total}} results", "SORT": {"SELECT_COLUMN_PLACEHOLDER": "Add another column to sort by", "SELECT_COLUMN_PLACEHOLDER_EMPTY_STATE": "Choose a column to sort by", "SELECT_COLUMN_NONE": "None", "SORT_COLUMN_ORDER_DESCRIPTION_TEXT": {"SORT_BY": "Sort by", "THEN_SORT_BY": "then by"}, "SORT_TOGGLE_LABELS": {"A_TO_Z": "A to Z", "Z_TO_A": "Z to A", "_1_TO_9": "1 to 9", "_9_TO_1": "9 to 1", "FIRST_TO_LAST": "First to last", "LAST_TO_FIRST": "Last to first", "EARLIEST_TO_LATEST": "Oldest first", "LATEST_TO_EARLIEST": "Newest first"}}, "EXPORT": {"BUTTON": "Export", "BUTTON_TOOLTIP_BEFORE_COUNT": "Export", "BUTTON_TOOLTIP_AFTER_COUNT": "items"}, "COLUMN_SELECTOR": {"BUTTON": "Columns"}}}, "STANDARD_FIELD_RULES": {"CONTACT": {"FIRST_OR_LAST_NAME_OR_PHONE_OR_EMAIL_REQUIRED": "One of first name, last name, phone or email is required"}, "COMPANY": {"COMPANY_NAME_REQUIRED": "Company name is required"}}}