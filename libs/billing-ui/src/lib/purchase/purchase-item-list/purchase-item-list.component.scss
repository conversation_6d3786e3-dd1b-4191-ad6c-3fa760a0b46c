@use 'design-tokens' as *;

.mat-mdc-table {
  border-collapse: separate;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  background-color: $primary-background-color;
  width: 100%;
  font-size: 12px;

  tr:hover {
    background: #f5f5f5;
  }

  thead th {
    color: rgba(0, 0, 0, 0.9);
    font-size: 12px;
    font-weight: 500;
    text-align: left;
  }
}

.no-purchases {
  background-color: $primary-background-color;
  display: flex;
  font-size: 12px;
  font-weight: 500;
  justify-content: space-around;
  border-collapse: separate;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

th[mat-header-cell] {
  position: relative;
}

.currency-header {
  text-align: right;
  padding: 20px;
}

.currency-data {
  text-align: right;
  padding: 20px;
}
