@let selectedConversation = currentConversationDetail()?.conversation;

<div class="inbox-chat">
  @if (loading()) {
    <glxy-loading-spinner class="show-loading"></glxy-loading-spinner>
  } @else if (!!selectedConversation && !!messages()) {
    @if (hasValidAssistant() && messages().length === 0) {
      <inbox-chat-empty-state
        class="no-messages-view"
        [title]="aiTitle()"
        [description]="aiDescription()"
        [iconName]="assistantIconName()"
        [avatarUrl]="assistantAvatarUrl()"
        [suggestionChips]="aiSuggestionChips()"
        (suggestionClicked)="sendSuggestionMessage($event)"
      ></inbox-chat-empty-state>
    } @else {
      <inbox-messages
        [messages]="(messages$ | async | handleEmptyMessage) ?? []"
        [conversation]="selectedConversation"
      ></inbox-messages>
    }
  }
  <inbox-message-text
    #inboxTextComponent
    (sendMessage)="sendMessage($event)"
    [conversationDetail]="currentConversationDetail$ | async"
    [availableChannels]="availableChannels"
    [isImpersonating]="!!(isImpersonating$ | async)"
  ></inbox-message-text>
</div>
