@use 'design-tokens' as *;
@use '../../styles/breaks.scss' as *;

:host {
  width: 100%;
}

.inbox-chat {
  background: $white;
  display: flex;
  flex-direction: column;
  height: 100%;
}

inbox-messages {
  flex: 1;
  min-height: 0;
}

.show-loading {
  flex: 1;
  min-height: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease-in-out;
}

inbox-message-text {
  width: 100%;
}

.ai-banner {
  z-index: 1;
}

@media screen and (min-width: $mobile + 1) {
  .ai-banner {
    border-left: none;
  }
}
