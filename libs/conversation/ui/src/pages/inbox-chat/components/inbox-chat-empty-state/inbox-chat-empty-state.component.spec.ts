import { ComponentFixture, TestBed } from '@angular/core/testing';
import { InboxChatEmptyStateComponent } from './inbox-chat-empty-state.component';
import { TranslateTestingModule } from 'ngx-translate-testing';

describe('InboxChatEmptyStateComponent', () => {
  let component: InboxChatEmptyStateComponent;
  let fixture: ComponentFixture<InboxChatEmptyStateComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [InboxChatEmptyStateComponent, TranslateTestingModule.withTranslations({})],
    }).compileComponents();

    fixture = TestBed.createComponent(InboxChatEmptyStateComponent);
    component = fixture.componentInstance;

    // Set required inputs
    component.title = 'Test Title';
    component.description = 'Test Description';

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit suggestionClicked when a suggestion is clicked', () => {
    const suggestionMessage = 'Test suggestion message';
    const emitSpy = jest.spyOn(component.suggestionClicked, 'emit');

    component.onSuggestionClick(suggestionMessage);

    expect(emitSpy).toHaveBeenCalledWith(suggestionMessage);
  });

  it('should accept suggestion chips input', () => {
    const suggestions = [
      { label: 'test.label1', message: 'Test message 1' },
      { label: 'test.label2', message: 'Test message 2' },
    ];

    component.suggestionChips = suggestions;

    expect(component.suggestionChips).toEqual(suggestions);
  });
});
