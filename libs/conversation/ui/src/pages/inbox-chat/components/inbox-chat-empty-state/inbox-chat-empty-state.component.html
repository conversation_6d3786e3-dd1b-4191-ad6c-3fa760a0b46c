<glxy-empty-state>
  <glxy-empty-state-hero>
    @if (iconName && !avatarUrl) {
      <mat-icon [svgIcon]="iconName" class="hero-element"></mat-icon>
    }
    @if (avatarUrl) {
      <img [src]="avatarUrl" [alt]="title" class="hero-element" />
    }
  </glxy-empty-state-hero>

  <glxy-empty-state-title>
    {{ title }}
  </glxy-empty-state-title>

  <p>
    {{ description }}
  </p>

  @if (suggestionChips.length > 0) {
    <div class="suggestion-chips">
      <glxy-badge
        *ngFor="let suggestion of suggestionChips"
        class="clickable-chip"
        (click)="onSuggestionClick(suggestion.message)"
        [attr.aria-label]="suggestion.label"
      >
        {{ suggestion.label }}
      </glxy-badge>
    </div>
  }
</glxy-empty-state>
