@use 'design-tokens' as *;

:host {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: $spacing-4;
}

.empty-state-icon {
  margin-bottom: $spacing-3;
  min-width: 60px;
  min-height: 60px;
}

.hero-element {
  width: 60px !important;
  height: 60px !important;
  border-radius: 50%;
}

.suggestion-chips {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: $spacing-4;
  width: 100%;
  flex-wrap: wrap;
  gap: $spacing-2;

  .clickable-chip {
    cursor: pointer;
  }
}
