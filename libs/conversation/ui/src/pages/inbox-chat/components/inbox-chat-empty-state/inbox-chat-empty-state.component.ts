import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';

export interface SuggestionChip {
  label: string; // Already translated text to display
  message: string; // The message to send when clicked
}

@Component({
  selector: 'inbox-chat-empty-state',
  imports: [CommonModule, MatIconModule, GalaxyBadgeModule, GalaxyEmptyStateModule],
  templateUrl: './inbox-chat-empty-state.component.html',
  styleUrl: './inbox-chat-empty-state.component.scss',
})
export class InboxChatEmptyStateComponent {
  @Input() title!: string;
  @Input() description!: string;
  @Input() avatarUrl?: string;
  @Input() iconName?: string;
  @Input() suggestionChips: SuggestionChip[] = [];

  @Output() suggestionClicked = new EventEmitter<string>();

  onSuggestionClick(suggestion: string): void {
    this.suggestionClicked.emit(suggestion);
  }
}
