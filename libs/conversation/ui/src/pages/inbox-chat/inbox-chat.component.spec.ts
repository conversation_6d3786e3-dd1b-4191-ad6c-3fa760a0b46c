import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, convertToParamMap, Router } from '@angular/router';
import { createComponentFactory, mockProvider, Spectator } from '@ngneat/spectator/jest';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  ConversationApiService,
  ConversationChannel,
  GlobalParticipantType,
  InboxApiService,
  Participant,
  ParticipantType,
  PlatformLocation,
} from '@vendasta/conversation';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { of, take } from 'rxjs';
import { ConversationService } from '../../../../core/src/lib/state/conversation.service';
import { InboxNotificationService } from '../../../../core/src/lib/inbox-notification.service';
import { InboxTermsOfService } from '../../../../core/src/lib/inbox-terms-of-service.service';
import { InboxService } from '../../../../core/src/lib/inbox.service';
import { ConversationDetail, TermsOfServiceResult } from '../../../../core/src/lib/interface/conversation.interface';
import {
  conversationServiceMock,
  inboxNotificationServiceMock,
  inboxServiceMock,
  participantServiceMock,
} from '../../../../core/src/lib/mocks';
import { ParticipantService } from '../../../../core/src/lib/participant.service';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  CONVERSATION_CHANNEL_SERVICE_TOKEN,
  CONVERSATION_HOST_APP_INTERFACE_TOKEN,
  CONVERSATION_ROUTES_TOKEN,
  GROUP_ID_TOKEN,
  PARTNER_BRAND_NAME_TOKEN,
  PARTNER_ID_TOKEN,
  USER_ID_TOKEN,
} from '../../../../core/src/lib/tokens';
import { ViewService } from '../../../../core/src/lib/view.service';
import { InboxChatComponent } from './inbox-chat.component';
import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { InboxMessageTextComponent } from './components/inbox-message-text/inbox-message-text.component';
import { WhatsappTemplatesService } from '../../../../core/src/lib/channels/whatsapp-templates.service';
import { EmptyMessagesPipe } from '../../../../pipes/src/message.pipes';
import { AiAssistantService } from '@galaxy/ai-assistant';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';

@Component({
  selector: 'inbox-message-text',
  template: '<div></div>',
  providers: [{ provide: InboxMessageTextComponent, useClass: MockInboxMessageTextComponent }],
  standalone: false,
})
class MockInboxMessageTextComponent {
  public clearText = () => null;
  public removeFile = () => null;
}

@Component({
  selector: 'inbox-messages-top-bar',
  template: '<div></div>',
  standalone: false,
})
class MockInboxMessagesTopBarComponent {}

@Component({
  selector: 'inbox-messages',
  template: '<div></div>',
  standalone: false,
})
class MockInboxMessagesComponent {}

@Component({
  selector: 'inbox-error',
  template: '<div></div>',
  standalone: false,
})
class MockInboxErrorComponent {}

describe('InboxChatComponent', () => {
  let spectator: Spectator<InboxChatComponent>;
  const translateServiceMock = {
    get: () => of(''),
    onTranslationChange: of(''),
    onLangChange: of(''),
    onDefaultLangChange: of(''),
  };

  const routerMock = {
    navigate: jest.fn(),
    events: of({}),
  };

  const routeMock = {
    data: of({
      firestoreConversation: {
        conversation: { subjectParticipantKeyIds: ['ABC', 'VNDR', 'AG-456'] },
      },
    }),
    queryParamMap: of(convertToParamMap({})),
    params: of({ id: 'C-123' }),
  };

  const inboxApiServiceMock = {
    getMultiConfiguration: jest.fn().mockReturnValue(of({ configurations: [] })),
  };

  const createComponent = createComponentFactory({
    component: InboxChatComponent,
    imports: [
      TranslateModule,
      GalaxyAlertModule,
      MatSidenavModule,
      MatIconModule,
      MatButtonModule,
      GalaxyLoadingSpinnerModule,
    ],
    providers: [
      { provide: Router, useValue: routerMock },
      { provide: ActivatedRoute, useValue: routeMock },
      { provide: TranslateService, useValue: translateServiceMock },
      {
        provide: ConversationService,
        useValue: conversationServiceMock,
      },
      { provide: PARTNER_BRAND_NAME_TOKEN, useValue: of('partner-brand') },
      {
        provide: InboxService,
        useValue: inboxServiceMock,
      },
      {
        provide: InboxTermsOfService,
        useValue: {
          getTermsOfServiceText$: of(''),
          termsOfServiceAccepted$: of(''),
          termsOfServiceAccepted$$: { next: jest.fn() },
        },
      },
      { provide: MatDialog, useValue: {} },
      { provide: InboxApiService, useValue: inboxApiServiceMock },
      { provide: InboxNotificationService, useValue: inboxNotificationServiceMock },
      { provide: ParticipantService, useValue: participantServiceMock },
      { provide: CONVERSATION_CHANNEL_SERVICE_TOKEN, useValue: { get: jest.fn() } },
      { provide: ACCOUNT_GROUP_ID_TOKEN, useValue: of('') },
      { provide: PARTNER_ID_TOKEN, useValue: of('') },
      { provide: CONVERSATION_ROUTES_TOKEN, useValue: of({}) },
      { provide: USER_ID_TOKEN, useValue: of('') },
      {
        provide: CONVERSATION_HOST_APP_INTERFACE_TOKEN,
        useValue: {
          getAppOptions: jest.fn().mockReturnValue({ is_multilocation: false }),
          isRecipientInternalInfoDeleted: jest.fn().mockReturnValue(false),
        },
      },
      { provide: GROUP_ID_TOKEN, useValue: of('') },
      {
        provide: ConversationApiService,
        useValue: {
          listMessages: jest.fn().mockReturnValue(of({ messages: [] })),
        },
      },
      {
        provide: AiAssistantService,
        useValue: {
          getSystemAssistant: jest.fn().mockReturnValue(of(null)),
          buildAssistantConfigurationUrl: jest.fn().mockReturnValue(of('')),
        },
      },
      {
        provide: 'ViewModeService',
        useValue: {
          toggleInboxOpen: jest.fn(),
          viewMode$: of('sidebar'),
          isInboxOpen$: of(false),
        },
      },
    ],
    mocks: [ViewService, ProductAnalyticsService, SnackbarService, WhatsappTemplatesService],
    declarations: [
      MockInboxMessageTextComponent,
      MockInboxMessagesTopBarComponent,
      MockInboxMessagesComponent,
      MockInboxErrorComponent,
      EmptyMessagesPipe,
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA],
  });

  describe('in partner center', () => {
    beforeEach(() => {
      conversationServiceMock.currentConversationDetail$ = of({
        conversation: {
          conversationId: 'C-123',
          subjectParticipants: [
            { internalParticipantId: 'ABC', participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_PARTNER },
            { internalParticipantId: 'VNDR', participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_VENDOR },
            {
              internalParticipantId: 'AG-456',
              participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP,
            },
          ],
        },
        participants: [
          new Participant({
            internalParticipantId: 'ABC',
            participantType: ParticipantType.PARTICIPANT_TYPE_PARTNER,
            isSubjectParticipant: true,
          }),
          new Participant({
            internalParticipantId: 'VNDR',
            participantType: ParticipantType.PARTICIPANT_TYPE_VENDOR,
            isSubjectParticipant: true,
          }),
          new Participant({
            internalParticipantId: 'AG-456',
            participantType: ParticipantType.PARTICIPANT_TYPE_ACCOUNT_GROUP,
            isSubjectParticipant: true,
          }),
        ],
      } as ConversationDetail);
    });

    describe('Conversation is loaded and shown on the screen', () => {
      it('should load the conversation for the same PID', () => {
        spectator = createComponent({
          detectChanges: false,
          providers: [{ provide: PARTNER_ID_TOKEN, useValue: of('ABC') }],
        });
        expect(routerMock.navigate).not.toHaveBeenCalled();
      });

      it('should load the conversation for the same vendorPartnerId', () => {
        spectator = createComponent({
          detectChanges: false,
          providers: [{ provide: PARTNER_ID_TOKEN, useValue: of('VNDR') }],
        });
        expect(routerMock.navigate).not.toHaveBeenCalled();
      });

      it('should redirect to home if PID is not in the subjectParticipantKeyIds', () => {
        spectator = createComponent({
          detectChanges: false,
          providers: [{ provide: PARTNER_ID_TOKEN, useValue: of('VUNI') }],
        });
        expect(routerMock.navigate).toHaveBeenCalled();
      });

      it('should redirect to modal root if PID change', () => {
        spectator = createComponent({
          detectChanges: false,
          providers: [
            { provide: PARTNER_ID_TOKEN, useValue: of('VUNI') },
            { provide: CONVERSATION_ROUTES_TOKEN, useValue: of({ useModal: true }) },
          ],
        });
        expect(routerMock.navigate).toHaveBeenCalledWith(['', { outlets: { inbox: 'inbox' } }]);
      });
    });

    describe('availability banner', () => {
      it('should get banners for partners and vendors', () => {
        conversationServiceMock.currentConversationDetail$ = of({
          conversation: {},
          participants: [
            new Participant({
              internalParticipantId: 'ABC',
              participantType: ParticipantType.PARTICIPANT_TYPE_PARTNER,
              isSubjectParticipant: true,
            }),
            new Participant({
              internalParticipantId: 'VNDR',
              participantType: ParticipantType.PARTICIPANT_TYPE_VENDOR,
              isSubjectParticipant: true,
            }),
          ],
        } as ConversationDetail);

        spectator = createComponent();

        expect(inboxApiServiceMock.getMultiConfiguration).toHaveBeenCalledWith({
          subjectParticipants: [
            {
              internalParticipantId: 'ABC',
              participantType: 4,
            },
            {
              internalParticipantId: 'VNDR',
              participantType: 4,
            },
          ],
        });
      });

      it('should filter out empty internal ids', () => {
        conversationServiceMock.currentConversationDetail$ = of({
          conversation: {},
          participants: [
            new Participant({
              internalParticipantId: '',
              participantType: ParticipantType.PARTICIPANT_TYPE_OPENAI_BOT,
              isSubjectParticipant: true,
            }),
            new Participant({
              internalParticipantId: 'ABC',
              participantType: ParticipantType.PARTICIPANT_TYPE_PARTNER,
              isSubjectParticipant: true,
            }),
          ],
        } as ConversationDetail);

        spectator = createComponent();

        expect(inboxApiServiceMock.getMultiConfiguration).toHaveBeenCalledWith({
          subjectParticipants: [
            {
              internalParticipantId: 'ABC',
              participantType: 4,
            },
          ],
        });
      });

      it('should filter out non-partner and vendor configs', () => {
        conversationServiceMock.currentConversationDetail$ = of({
          conversation: {},
          participants: [
            new Participant({
              internalParticipantId: 'AG-123',
              participantType: ParticipantType.PARTICIPANT_TYPE_ACCOUNT_GROUP,
              isSubjectParticipant: true,
            }),
            new Participant({
              internalParticipantId: 'ABC',
              participantType: ParticipantType.PARTICIPANT_TYPE_PARTNER,
              isSubjectParticipant: true,
            }),
            new Participant({
              internalParticipantId: 'DEF',
              participantType: ParticipantType.PARTICIPANT_TYPE_VENDOR,
              isSubjectParticipant: true,
            }),
            new Participant({
              internalParticipantId: 'C-123',
              participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
              isSubjectParticipant: true,
            }),
          ],
        } as ConversationDetail);

        spectator = createComponent();

        expect(inboxApiServiceMock.getMultiConfiguration).toHaveBeenCalledWith({
          subjectParticipants: [
            {
              internalParticipantId: 'ABC',
              participantType: 4,
            },
            {
              internalParticipantId: 'DEF',
              participantType: 4,
            },
          ],
        });
      });
    });

    describe('handle TOS display when sending message', () => {
      const mockResult$ = (text: string, accepted: boolean) =>
        of({
          termsOfServiceAccepted: accepted,
          termsOfServiceTextMessage: text,
        } as TermsOfServiceResult);

      describe('for an unfollowed conversation', () => {
        beforeEach(() => {
          jest.clearAllMocks();
          spectator = createComponent({
            detectChanges: false,
          });
        });

        it('should not show TOS dialog', async () => {
          Object.defineProperty(spectator.component, 'termsOfServiceResult$', {
            value: mockResult$('TOS text', true),
          });
          spectator.component['sendMessageConversation'] = jest.fn().mockReturnValueOnce(of({ messageId: 'M-123' }));
          spectator.component['showTermsOfService'] = jest.fn();
          spectator.component['isFollowing$$'].next = jest.fn();

          await spectator.component.sendMessage({
            text: 'test message',
            channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
            attachments: [],
          });

          expect(spectator.component['showTermsOfService']).not.toHaveBeenCalled();
          expect(spectator.component['sendMessageConversation']).toHaveBeenCalled();
          expect(spectator.component['isFollowing$$'].next).toHaveBeenCalledWith(true);
          expect(spectator.component['snackbarService'].openSuccessSnack).toHaveBeenCalled();
        });

        it('should show TOS dialog', async () => {
          spectator.component['sendMessageConversation'] = jest.fn();
          spectator.component['showTermsOfService'] = jest.fn();
          Object.defineProperty(spectator.component, 'termsOfServiceResult$', {
            value: mockResult$('TOS text', false),
          });
          spectator.component['isFollowing$$'].next = jest.fn();

          await spectator.component.sendMessage({
            text: 'test',
            channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
            attachments: [],
          });

          expect(spectator.component['showTermsOfService']).toHaveBeenCalled();
          expect(spectator.component['sendMessageConversation']).not.toHaveBeenCalled();
          expect(spectator.component['isFollowing$$'].next).not.toHaveBeenCalled();
          expect(spectator.component['snackbarService'].openSuccessSnack).not.toHaveBeenCalled();
        });
      });

      describe('for a followed conversation', () => {
        beforeEach(() => {
          spectator = createComponent({
            detectChanges: false,
          });
          spectator.component['isFollowing$$'].next(true);
        });

        it('should not show TOS dialog', async () => {
          Object.defineProperty(spectator.component, 'termsOfServiceResult$', {
            value: mockResult$('TOS text', true),
          });
          spectator.component['showTermsOfService'] = jest.fn();

          await spectator.component.sendMessage({
            text: 'test message',
            channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
            attachments: [],
          });

          expect(spectator.component['showTermsOfService']).not.toHaveBeenCalled();
          expect(conversationServiceMock.sendAndStageMessage).toHaveBeenCalled();
          expect(spectator.component['snackbarService'].openSuccessSnack).not.toHaveBeenCalled();
        });

        it('should show TOS dialog', async () => {
          spectator.component['showTermsOfService'] = jest.fn();
          Object.defineProperty(spectator.component, 'termsOfServiceResult$', {
            value: mockResult$('TOS text', false),
          });

          await spectator.component.sendMessage({
            text: 'test',
            channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
            attachments: [],
          });

          expect(spectator.component['showTermsOfService']).toHaveBeenCalled();
          expect(conversationServiceMock.sendAndStageMessage).not.toHaveBeenCalled();
          expect(spectator.component['snackbarService'].openSuccessSnack).not.toHaveBeenCalled();
        });
      });
    });

    describe('updateFollowStatus', () => {
      beforeEach(() => {
        spectator = createComponent({
          detectChanges: false,
        });

        const d = {
          conversation: {
            conversationId: 'C-123',
          },
        } as ConversationDetail;
        conversationServiceMock.currentConversationDetail$ = of(d);
        spectator.component['isFollowing$$'].next = jest.fn();
      });

      it('should update following status to false correctly', () => {
        spectator.component.updateFollowStatus(false);
        spectator.component['viewService'].removeConversationFromConversationView = jest
          .fn()
          .mockReturnValue(of({} as HttpResponse<null>));
        expect(spectator.component['snackbarService'].openSuccessSnack).not.toHaveBeenCalled();
        spectator.component['isFollowing$'].pipe(take(1)).subscribe((value) => {
          expect(value).toEqual(false);
        });
      });

      it('should error if endpoint error', () => {
        const httpError = { error: { message: 'error' } } as HttpErrorResponse;
        spectator.component['viewService'].addConversationToConversationView = jest.fn().mockImplementation(() => {
          throw httpError;
        });
        spectator.component
          .updateFollowStatus(true)
          .then(() => expect(spectator.component['snackbarService'].openErrorSnack).toHaveBeenCalled());
      });

      it('should update following status correctly', async () => {
        spectator.component['viewService'].addConversationToConversationView = jest
          .fn()
          .mockReturnValue(of({} as HttpResponse<null>));

        await spectator.component['viewService'].addConversationToConversationView('C-123');

        await spectator.component.updateFollowStatus(true).then(() => {
          expect(spectator.component['snackbarService'].openSuccessSnack).toHaveBeenCalled();
          expect(spectator.component['isFollowing$$'].next).toHaveBeenCalledWith(true);
        });
      });
    });
  });

  describe('in business center', () => {
    describe('Conversation is loaded and shown on the screen', () => {
      let d: ConversationDetail;
      beforeEach(() => {
        d = {
          conversation: {
            subjectParticipants: [
              { internalParticipantId: 'ABC', participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_PARTNER },
              { internalParticipantId: 'VNDR', participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_VENDOR },
              {
                internalParticipantId: 'AG-456',
                participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP,
              },
            ],
          },
          participants: [
            new Participant({
              internalParticipantId: 'ABC',
              participantType: ParticipantType.PARTICIPANT_TYPE_PARTNER,
              isSubjectParticipant: true,
            }),
            new Participant({
              internalParticipantId: 'VNDR',
              participantType: ParticipantType.PARTICIPANT_TYPE_VENDOR,
              isSubjectParticipant: true,
            }),
            new Participant({
              internalParticipantId: 'AG-456',
              participantType: ParticipantType.PARTICIPANT_TYPE_ACCOUNT_GROUP,
              isSubjectParticipant: true,
            }),
          ],
        } as ConversationDetail;
        conversationServiceMock.currentConversationDetail$ = of(d);
        inboxServiceMock.isBusinessApp = true;
        inboxServiceMock.platformLocation = PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP;
      });

      it('should redirect to home if PID change in business app', () => {
        spectator = createComponent({
          providers: [{ provide: ACCOUNT_GROUP_ID_TOKEN, useValue: of('AG-123') }],
        });
        expect(routerMock.navigate).toHaveBeenCalled();
      });

      it('should load the conversation for the same accountGroupId in business app', () => {
        spectator = createComponent({
          providers: [{ provide: ACCOUNT_GROUP_ID_TOKEN, useValue: of('AG-456') }],
        });
        expect(routerMock.navigate).not.toHaveBeenCalled();
      });
    });

    describe('handle TOS display when sending message', () => {
      const mockResult$ = (text: string, accepted: boolean) =>
        of({
          termsOfServiceAccepted: accepted,
          termsOfServiceTextMessage: text,
        } as TermsOfServiceResult);

      beforeEach(() => {
        spectator = createComponent({
          detectChanges: false,
          providers: [
            { provide: ACCOUNT_GROUP_ID_TOKEN, useValue: of('AG-456') },
            mockProvider(InboxService, {
              platformLocation: PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP,
              isBusinessApp: true,
            }),
          ],
        });
      });

      it('should not show TOS dialog and not follow nor show success snack for business app', async () => {
        Object.defineProperty(spectator.component, 'termsOfServiceResult$', {
          value: mockResult$('TOS text', true),
        });

        spectator.component['showTermsOfService'] = jest.fn();

        await spectator.component.sendMessage({
          text: 'test message',
          channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
          attachments: [],
        });

        expect(spectator.component['showTermsOfService']).not.toHaveBeenCalled();
        expect(conversationServiceMock.sendAndStageMessage).toHaveBeenCalled();
        const snackbarService = spectator.inject(SnackbarService);
        expect(snackbarService.openSuccessSnack).not.toHaveBeenCalled();
      });
    });

    describe('updateFollowStatus', () => {
      let d: ConversationDetail;
      beforeEach(() => {
        d = {
          conversation: {},
          participants: [
            new Participant({
              internalParticipantId: 'ABC',
              participantType: ParticipantType.PARTICIPANT_TYPE_PARTNER,
              isSubjectParticipant: true,
            }),
            new Participant({
              internalParticipantId: 'VNDR',
              participantType: ParticipantType.PARTICIPANT_TYPE_VENDOR,
              isSubjectParticipant: true,
            }),
            new Participant({
              internalParticipantId: 'AG-456',
              participantType: ParticipantType.PARTICIPANT_TYPE_ACCOUNT_GROUP,
              isSubjectParticipant: true,
            }),
          ],
        } as ConversationDetail;
        conversationServiceMock.currentConversationDetail$ = of(d);
        inboxServiceMock.isBusinessApp = true;
        inboxServiceMock.platformLocation = PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP;
      });

      beforeEach(() => {
        spectator = createComponent({
          detectChanges: false,
          providers: [
            mockProvider(InboxService, {
              platformLocation: PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP,
              isBusinessApp: true,
            }),
          ],
        });
        spectator.component['loadConversation'] = jest.fn();
        spectator.component['conversationService'].displayAlertIfExists = jest.fn();
      });

      it('should not update following status for business app', (done) => {
        spectator.component
          .updateFollowStatus(true)
          .then(() => expect(spectator.component['snackbarService'].openSuccessSnack).not.toHaveBeenCalled());
        spectator.component['isFollowing$'].subscribe((value) => {
          expect(value).toEqual(false);
          done();
        });
      });
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
});
