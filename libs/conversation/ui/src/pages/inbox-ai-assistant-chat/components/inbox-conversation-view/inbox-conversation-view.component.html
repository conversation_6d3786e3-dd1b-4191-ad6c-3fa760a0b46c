@let selectedConversation = currentConversationDetail()?.conversation;

<div class="inbox-chat">
  @if (loading()) {
    <glxy-loading-spinner class="show-loading"></glxy-loading-spinner>
  } @else if (!!selectedConversation && !!messages()) {
    @if (hasValidAssistant() && messages().length === 0) {
      <inbox-chat-empty-state
        class="no-messages-view"
        [title]="aiTitle()"
        [description]="aiDescription()"
        [iconName]="assistantIconName()"
        [avatarUrl]="assistantAvatarUrl()"
        [suggestionChips]="aiSuggestionChips()"
        (suggestionClicked)="sendSuggestionMessage($event)"
      ></inbox-chat-empty-state>
    } @else {
      <inbox-messages
        class="inbox-messages"
        [messages]="messages()"
        [conversation]="selectedConversation"
      ></inbox-messages>
    }

    <inbox-message-text
      #inboxTextComponent
      (sendMessage)="sendMessage($event)"
      [conversationDetail]="currentConversationDetail() ?? null"
      [availableChannels]="availableChannels"
      [isImpersonating]="!!(isImpersonating$ | async)"
    ></inbox-message-text>
  } @else {
    <inbox-no-conversations-view class="no-conversations-view"></inbox-no-conversations-view>
  }
</div>
