import { ComponentFixture, TestBed } from '@angular/core/testing';
import { InboxConversationViewComponent } from './inbox-conversation-view.component';
import { AiAssistantConversationService, ConversationService, InboxService } from '../../../../../../core/src';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { CommonModule } from '@angular/common';
import { ConversationUIModule } from '../../../../';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import {
  DEFAULT_LANGUAGE,
  TranslateModule,
  TranslateStore,
  USE_DEFAULT_LANG,
  USE_EXTEND,
  USE_STORE,
} from '@ngx-translate/core';
import { LEXICON_DISABLE_OTW, LEXICON_FILE_FORMAT } from '@galaxy/lexicon';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { AiAssistantService } from '@galaxy/ai-assistant';
import { ACCOUNT_GROUP_ID_TOKEN, PARTNER_ID_TOKEN } from '../../../../../../core/src/lib/tokens';

describe('InboxAiAssistantChatComponent', () => {
  let component: InboxConversationViewComponent;
  let fixture: ComponentFixture<InboxConversationViewComponent>;

  beforeEach(async () => {
    const conversationService = {
      messages$: of([]),
      currentConversationDetail$: of(null),
      loadingMessages$: of(false),
      isUserImpersonated$: of(false),
      setCurrentFirestoreConversationId: jest.fn(),
      setConversationLastSeen: jest.fn(),
      sendAndStageMessage: jest.fn(),
      currentFirestoreConversationId$: of(''),
    };

    const inboxService = {
      platformLocation: 'test-location',
    };

    const aiAssistantService = {
      getAssistant: jest.fn().mockReturnValue(of(null)),
      getSystemAssistant: jest.fn().mockReturnValue(of(null)),
      buildAssistantConfigurationUrl: jest.fn().mockReturnValue(of('')),
    };

    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        ConversationUIModule,
        GalaxyLoadingSpinnerModule,
        GalaxyPageModule,
        TranslateModule.forRoot(),
        InboxConversationViewComponent,
        HttpClientTestingModule,
        TranslateTestingModule.withTranslations({}),
      ],
      providers: [
        { provide: ConversationService, useValue: conversationService },
        { provide: InboxService, useValue: inboxService },
        { provide: AiAssistantService, useValue: aiAssistantService },
        { provide: PARTNER_ID_TOKEN, useValue: of('test-partner-id') },
        { provide: ACCOUNT_GROUP_ID_TOKEN, useValue: of('test-account-group-id') },
        { provide: ActivatedRoute, useValue: { paramMap: of({ get: () => null }) } },
        {
          provide: AiAssistantConversationService,
          useValue: {
            aiAssistantId$: of('test-assistant-id'),
          },
        },
        { provide: TranslateStore, useValue: TranslateStore },
        { provide: LEXICON_DISABLE_OTW, useValue: {} },
        { provide: LEXICON_FILE_FORMAT, useValue: {} },
        { provide: USE_DEFAULT_LANG, useValue: true },
        { provide: USE_STORE, useValue: true },
        { provide: USE_EXTEND, useValue: true },
        { provide: DEFAULT_LANGUAGE, useValue: '' },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(InboxConversationViewComponent);
    fixture.componentRef.setInput('isLoading', false);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
