import { Component, computed, effect, inject, input, Signal, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { toSignal } from '@angular/core/rxjs-interop';
import { Observable, combineLatest, switchMap, of, catchError } from 'rxjs';
import { ConversationChannel, MessageType, SetLastSeenRequestStatus } from '@vendasta/conversation';
import {
  AiAssistantConversationService,
  ConversationAvailableChannels,
  ConversationService,
  fromFirestoreId,
  InboxService,
  MessageInfo,
  toFirestoreId,
  buildAIContextMetadata,
} from '../../../../../../core/src/index';
import { ConversationUIModule, InboxMessageTextComponent } from '../../../../';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { TranslateModule } from '@ngx-translate/core';
import { InboxNoConversationsViewComponent } from '../inbox-no-conversations-view/inbox-no-conversations-view.component';
import {
  InboxChatEmptyStateComponent,
  SuggestionChip,
} from '../../../inbox-chat/components/inbox-chat-empty-state/inbox-chat-empty-state.component';
import { DEFAULT_AVATAR_SVG_ICON, AiAssistantService } from '@galaxy/ai-assistant';
import { TranslateService } from '@ngx-translate/core';
import { ACCOUNT_GROUP_ID_TOKEN, PARTNER_ID_TOKEN } from '../../../../../../core/src/lib/tokens';

@Component({
  selector: 'inbox-conversation-view',
  imports: [
    CommonModule,
    ConversationUIModule,
    GalaxyLoadingSpinnerModule,
    GalaxyPageModule,
    TranslateModule,
    InboxNoConversationsViewComponent,
    InboxChatEmptyStateComponent,
  ],
  templateUrl: './inbox-conversation-view.component.html',
  styleUrl: './inbox-conversation-view.component.scss',
})
export class InboxConversationViewComponent {
  @ViewChild('inboxTextComponent') inboxTextComponent?: InboxMessageTextComponent;

  private readonly conversationService = inject(ConversationService);
  private readonly inboxService = inject(InboxService);
  private readonly aiConversationService = inject(AiAssistantConversationService);
  private readonly translateService = inject(TranslateService);
  private readonly aiAssistantService = inject(AiAssistantService);
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN);
  private readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN);

  // This is an awkward workaround to get the loading state of the conversation without relying on the router or ConversationListService
  readonly isLoading = input.required<boolean>();

  readonly messages = toSignal(this.conversationService.messages$, { initialValue: [] });
  readonly conversationId = toSignal(this.conversationService.currentFirestoreConversationId$);
  readonly currentConversationDetail = toSignal(this.conversationService.currentConversationDetail$);
  readonly loadingMessages = toSignal(this.conversationService.loadingMessages$);
  readonly loadingConversation: Signal<boolean>;
  readonly loading = computed(() => this.loadingConversation() || this.loadingMessages() || this.isLoading());
  readonly assistantId = toSignal(this.aiConversationService.aiAssistantId$);

  readonly isImpersonating$: Observable<boolean> = this.conversationService.isUserImpersonated$;

  readonly currentAssistant = toSignal(
    combineLatest([this.aiConversationService.aiAssistantId$, this.partnerId$, this.accountGroupId$]).pipe(
      switchMap(([assistantId, partnerId, accountGroupId]) => {
        if (!assistantId || !partnerId || !accountGroupId) {
          return of(null);
        }
        return this.aiAssistantService.getAssistant(partnerId, accountGroupId, assistantId).pipe(
          catchError((err) => {
            console.error('Error getting AI assistant information', err);
            return of(null);
          }),
        );
      }),
    ),
    { initialValue: null },
  );

  readonly dynamicAssistantName = computed(() => {
    const assistant = this.currentAssistant();
    return assistant?.name || null;
  });

  readonly assistantAvatarUrl = computed(() => {
    const assistant = this.currentAssistant();
    return assistant?.avatarUrl;
  });

  readonly assistantIconName = computed(() => {
    return DEFAULT_AVATAR_SVG_ICON;
  });

  readonly systemAssistant = toSignal(
    this.aiAssistantService.getSystemAssistant().pipe(
      catchError((err) => {
        console.error('Error getting system AI assistant information', err);
        return of(null);
      }),
    ),
    { initialValue: null },
  );

  // Show empty state for any valid assistant, not just the system assistant
  readonly hasValidAssistant = computed(() => {
    const assistant = this.currentAssistant();
    return !!assistant?.name;
  });

  // Check if current assistant is the system assistant
  readonly isSystemAssistant = computed(() => {
    const assistant = this.currentAssistant();
    const systemAssistant = this.systemAssistant();
    return !!assistant && !!systemAssistant && assistant.id === systemAssistant.id;
  });

  // Only return suggestion chips for the system assistant
  readonly aiSuggestionChips = computed(() => {
    if (!this.isSystemAssistant()) {
      return [];
    }

    const suggestionKeys = [
      'INBOX.AI_CHAT.SUGGESTIONS.HELP_GETTING_STARTED',
      'INBOX.AI_CHAT.SUGGESTIONS.WALK_THROUGH_FEATURE',
      'INBOX.AI_CHAT.SUGGESTIONS.HELP_FIXING_SOMETHING',
    ];

    return suggestionKeys.map(
      (key): SuggestionChip => ({
        label: this.translateService.instant(key), // Translated text for display
        message: this.translateService.instant(key), // Same translated text for the message
      }),
    );
  });

  readonly aiTitle = computed(() => {
    const assistantName = this.dynamicAssistantName();
    return this.translateService.instant('INBOX.AI_CHAT.NO_MESSAGES.TITLE', { assistantName });
  });

  readonly aiDescription = computed(() => {
    const assistantName = this.dynamicAssistantName();
    const descriptionKey = this.isSystemAssistant()
      ? 'INBOX.AI_CHAT.NO_MESSAGES.DESCRIPTION_WITH_SUGGESTIONS'
      : 'INBOX.AI_CHAT.NO_MESSAGES.DESCRIPTION_WITHOUT_SUGGESTIONS';
    return this.translateService.instant(descriptionKey, { assistantName });
  });

  readonly availableChannels: ConversationAvailableChannels = {
    availableChannels: [ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT],
    preferredChannel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
    channelAvailabilities: [
      {
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        isAvailable: true,
      },
    ],
  };

  constructor() {
    this.loadingConversation = computed(() => {
      const id = this.conversationId();
      const detail = this.currentConversationDetail();
      return Boolean(id && (!detail || toFirestoreId(detail.conversation.conversationId) !== toFirestoreId(id)));
    });

    effect(() => {
      const id = this.conversationId();
      if (id) {
        this.conversationService.setConversationLastSeen(
          fromFirestoreId(id),
          SetLastSeenRequestStatus.SET_LAST_SEEN_REQUEST_STATUS_READ,
        );
      }
    });
  }

  async sendMessage(messageInfo: MessageInfo): Promise<void> {
    const currentConversationDetail = this.currentConversationDetail();
    if (!currentConversationDetail) {
      return;
    }
    if (currentConversationDetail.conversation.channel !== ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT) {
      // Just in case the first non-null conversation we load isn't an AI conversation
      // we don't want messages intended for AI appearing in other conversations
      return;
    }

    // Add current page URL to metadata for AI context
    const metadata = buildAIContextMetadata();

    await this.conversationService.sendAndStageMessage(
      currentConversationDetail.conversation.conversationId,
      MessageType.MESSAGE_TYPE_MESSAGE,
      messageInfo.text ?? '',
      messageInfo.channel,
      this.inboxService.platformLocation,
      messageInfo.attachments,
      undefined,
      undefined,
      metadata,
    );
    this.inboxTextComponent?.clearText?.();
  }

  async sendSuggestionMessage(message: string): Promise<void> {
    const messageInfo: MessageInfo = {
      text: message,
      channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
      attachments: [],
    };

    await this.sendMessage(messageInfo);
  }
}
