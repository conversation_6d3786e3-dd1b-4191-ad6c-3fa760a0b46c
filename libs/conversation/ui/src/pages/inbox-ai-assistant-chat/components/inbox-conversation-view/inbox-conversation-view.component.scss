@use 'design-tokens' as *;
@use '../../../../styles/breaks.scss' as *;

:host {
  width: 100%;
}

.inbox-chat {
  background: $card-background-color;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.inbox-messages {
  flex: 1;
  min-height: 0;
}

.show-loading {
  transition: all 0.3s ease-in-out;
}

inbox-message-text {
  width: 100%;
}

.no-conversations-view {
  height: 100%;
  background-color: $primary-background-color;
}

.no-messages-view {
  flex: 1;
  min-height: 0;
  background-color: $primary-background-color;
}
