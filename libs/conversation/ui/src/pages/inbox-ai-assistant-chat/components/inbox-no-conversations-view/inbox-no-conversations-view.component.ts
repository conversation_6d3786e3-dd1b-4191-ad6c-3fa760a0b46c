import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { AiAssistantConversationService } from '../../../../../../core/src/lib/state/ai-assistant-conversation.service';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'inbox-no-conversations-view',
  standalone: true,
  templateUrl: './inbox-no-conversations-view.component.html',
  styleUrl: './inbox-no-conversations-view.component.scss',
  imports: [CommonModule, TranslateModule, MatButtonModule],
})
export class InboxNoConversationsViewComponent {
  private readonly aiConversationService = inject(AiAssistantConversationService);

  readonly aiAssistantId$ = this.aiConversationService.aiAssistantId$;

  createConversation() {
    return this.aiConversationService.startConversation();
  }
}
