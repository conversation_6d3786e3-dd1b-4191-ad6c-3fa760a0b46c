@use '../../shared-styles/automations-shared-styles' as *;
@use 'design-tokens' as *;
@include automations-editor-core();

/* Adjusts the position of the checkbox to be in line with the searchbar and other items. */
glxy-checkbox {
  margin-top: $spacing-1;
  margin-bottom: $spacing-2;

  // The inner checkbox padding won't budge, so we use this as a workaround.
  // Note that no standard sizing exists to get the exact position we need.
  margin-left: -6px;
}

input.search-box {
  display: block;
  font-family: inherit;
  width: 100%;
  font-size: $font-preset-4-size;
  padding: $spacing-2 $spacing-2;
  border-radius: $spacing-1;
  background: $white;
  border: 1px solid $border-color;
  box-sizing: border-box;
  box-shadow: 0 0 5px rgba(51, 170, 255, 0);
  transition: box-shadow linear 50ms;

  // background: #f5f5f5;
  color: $primary-text-color;

  &:focus {
    border-color: #7dafe8;
    outline: none;
    box-shadow: 0 0 5px #80caff;
  }
  &::placeholder {
    color: $tertiary-font-color;
  }
}

.no-results {
  color: $secondary-text-color;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50%;
  width: 100%;
}

.loading-card {
  height: 100px;
  width: 100%;
  margin-bottom: $spacing-3;
}

.toggle-buttons {
  width: 100%;
  padding: $spacing-2 $spacing-3 0 $spacing-4;

  ::ng-deep .glxy-button-group-body {
    width: 100%;
  }
}

.toggle-button {
  width: 50%;
  min-width: 95px;
}

.search-bar {
  margin-top: $spacing-3;
}

.button-selected {
  background-color: $glxy-grey-200;
}

.new-badge {
  margin-bottom: 2px;
}
