@use '../control-theming' as *;
@use 'design-tokens' as *;

:host {
  display: inline-block;
  height: 24px;
  max-width: 100%;
  line-height: 24px;
  white-space: nowrap;
  outline: 0;
  user-select: none;
}

.atlas-navbar__slide-toggle__label {
  display: flex;
  flex: 1;
  flex-direction: row;
  align-items: center;
  height: inherit;
  cursor: pointer;
}

.atlas-navbar__slide-toggle-bar {
  transition: all 80ms linear;
  transition-property: background-color;
  transition-delay: 50ms;
  position: relative;
  width: 36px;
  height: 14px;
  flex-shrink: 0;
  border-radius: 8px;
  margin-right: 8px;
  margin-left: 0;
  background-color: rgba(0, 0, 0, 0.35);
}

.atlas-navbar__slide-toggle__input {
  bottom: 0;
  left: 10px;
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
  outline: 0;
  box-sizing: border-box;
  background-color: initial;
  cursor: default;
}

.atlas-navbar__slide-toggle__thumb-container {
  touch-action: none;
  user-select: none;
  position: absolute;
  z-index: 1;
  width: 20px;
  height: 20px;
  top: -3px;
  left: 0;
  transform: translate3d(0, 0, 0);
  transition: all 80ms linear;
  transition-property: transform;
}

.atlas-navbar__slide-toggle__label--checked .atlas-navbar__slide-toggle__thumb-container {
  transform: translate3d(16px, 0, 0);
}

.atlas-navbar__slide-toggle__thumb {
  transition: all 80ms linear;
  transition-property: background-color;
  transition-delay: 50ms;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  box-shadow:
    0px 2px 1px -1px rgba(0, 0, 0, 0.2),
    0px 1px 1px 0px rgba(0, 0, 0, 0.14),
    0px 1px 3px 0px rgba(0, 0, 0, 0.12);
  background-color: $primary-background-color;
}

.atlas-navbar__slide-toggle__overlay {
  position: absolute;
  top: calc(50% - 20px);
  left: calc(50% - 20px);
  height: 40px;
  width: 40px;
  z-index: -1;
  pointer-events: none;
  overflow: hidden;
}

.atlas-navbar__slide-toggle__overlay__element {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  transition:
    opacity,
    transform 0s cubic-bezier(0, 0, 0.2, 1);
  width: 100%;
  height: 100%;
  transform: none;
}

.atlas-navbar__slide-toggle__content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.atlas-navbar__slide-toggle__label--default {
  &.atlas-navbar__slide-toggle__label--checked {
    .atlas-navbar__slide-toggle__thumb {
      background-color: $default-background-color;
    }
    .atlas-navbar__slide-toggle-bar {
      background-color: $default-secondary-color;
    }
  }

  .atlas-navbar__slide-toggle-bar {
    &:hover {
      .atlas-navbar__slide-toggle__overlay__element {
        background: $default-hover-color;
      }
    }

    &:active {
      .atlas-navbar__slide-toggle__overlay__element {
        background: $default-active-color;
      }
    }
  }
}

.atlas-navbar__slide-toggle__label--primary {
  &.atlas-navbar__slide-toggle__label--checked {
    .atlas-navbar__slide-toggle__thumb {
      background-color: var(--themingPrimaryColor);
    }
    .atlas-navbar__slide-toggle-bar {
      background-color: var(--themingSecondaryColor);
    }
  }

  .atlas-navbar__slide-toggle-bar {
    &:hover {
      .atlas-navbar__slide-toggle__overlay__element {
        background: var(--themingPrimaryHoverColor);
      }
    }

    &:active {
      .atlas-navbar__slide-toggle__overlay__element {
        background: var(--themingPrimaryActiveColor);
      }
    }
  }
}

.atlas-navbar__slide-toggle__label--secondary {
  &.atlas-navbar__slide-toggle__label--checked {
    .atlas-navbar__slide-toggle__thumb {
      background-color: var(--themingFontColor);
      border: 1px solid var(--themingBorderColor);
    }
    .atlas-navbar__slide-toggle-bar {
      background-color: var(--themingFontColor);
    }
  }

  .atlas-navbar__slide-toggle-bar {
    &:hover {
      .atlas-navbar__slide-toggle__overlay__element {
        background: var(--themingSecondaryHoverColor);
      }
    }

    &:active {
      .atlas-navbar__slide-toggle__overlay__element {
        background: var(--themingSecondaryActiveColor);
      }
    }
  }
}
