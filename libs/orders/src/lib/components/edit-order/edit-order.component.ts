import {
  Component,
  DestroyRef,
  EventEmitter,
  Inject,
  Input,
  OnDestroy,
  OnInit,
  Output,
  signal,
  ViewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { AccountGroup } from '@galaxy/account-group';
import { BillingService, Currency } from '@galaxy/billing';
import { Environment, EnvironmentService } from '@galaxy/core';
import { FeatureFlagService } from '@galaxy/partner';
import { TranslateService } from '@ngx-translate/core';
import { ProductActivationPrereqFormComponent } from '@vendasta/businesses';
import {
  ConfirmationModalMaxWidth,
  ConfirmationModalWidth,
  OpenConfirmationModalService,
} from '@vendasta/galaxy/confirmation-modal';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { FulfillmentFormConfigInterface, FulfillmentOrder, FulfillmentOrderStatus } from '@vendasta/order-fulfillment';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import {
  ActivationStatus,
  CommonField,
  ConfigInterface,
  Cost,
  CustomField,
  CustomPriceMapping,
  Field,
  LineItem,
  Order,
  ProductActivation,
  SalesOrdersService,
  Status,
  UserInterface as User,
  ValidationErrorCodes,
} from '@vendasta/sales-orders';
import { OrderFormOptionsInterface } from '@vendasta/store';
import {
  WorkOrderDetailsComponent,
  WorkOrderFormConfigService,
  WorkOrderPersona,
  WorkOrderSalesOrderSummaryService,
} from '@vendasta/work-order';
import { BehaviorSubject, combineLatest, EMPTY, interval, merge, Observable, of, Subject, Subscription } from 'rxjs';
import {
  catchError,
  concatMap,
  debounceTime,
  delay,
  distinctUntilChanged,
  filter,
  first,
  map,
  mergeMap,
  shareReplay,
  skipWhile,
  startWith,
  switchMap,
  take,
  tap,
  withLatestFrom,
} from 'rxjs/operators';
import { Features } from '../../core/features';
import { OrderFeature } from '../../core/permissions';
import { OrderAction } from '../../core/permissions/permissions';
import { OrderPermissionsService } from '../../core/permissions/permissions.service';
import { PAGE_ORDER_CONFIG_TOKEN, PageOrderConfig } from '../../core/tokens';
import { DEMO_SSC_UPLOAD_URL, PROD_SSC_UPLOAD_URL } from '../../shared/constants';
import { ActivationDateConfirmationDialogComponent } from '../activation-date-confirmation-dialog/activation-date-confirmation-dialog.component';
import { LineItemsComponent } from '../line-items/line-items.component';
import { OrderFormComponent } from '../order-form/order-form.component';
import { AppVariablePriceMap, PackageVariablePriceMap } from '../variable-prices/variable-prices.component';
import { VariablePricesService } from '../variable-prices/variable-prices.service';
import { CancelOrderDialogComponent } from './dialogs/cancel-order/cancel-order-dialog.component';
import { DeclineCancellationDialogComponent } from './dialogs/decline-cancellation/decline-cancellation-dialog.component';
import { format, isBefore, isSameDay } from 'date-fns';
import { ProjectsAndSubtasksByProductsService, ProjectTrackerInfo } from '@vendasta/project-tracker';
import { PersonaType } from '@vendasta/iam';
import { OrderDeclineDialogComponent } from './dialogs/order-decline-dialog/order-decline-dialog.component';
import { AppType } from '@vendasta/marketplace-apps';
import { RetailSummary } from '../retail-summary/retail-summary.component';
import { OrderLineItemValidationService } from '../order-line-item-validation/order-line-item-validation.service';
import { getRetailSummaryFromOrder } from '../../shared/utils';
import { OrdersEditOrderFormValidationService } from './edit-order-form-validation.service';
import { OrdersEditTabService } from './edit-order-tab.service';
import { WholesaleSummary } from '@galaxy/inventory-ui';
import { DynamicOrderFormAppIds } from '../order-form/godaddy-order-form/dynamic-order-form-page.component';

import { PackageService } from '@vendasta/marketplace-packages';
import { AppPartnerService } from '@galaxy/marketplace-apps';
import { App, AppKey } from '@vendasta/marketplace-apps';

const INVALID_APPS_IN_ORDER = 'Invalid apps in order';
const ORDER_DETAIL_DIALOG_WIDTH = '660px';
const ORDER_DETAIL_DIALOG_HEIGHT = '90vh';

interface fulfillmentStatusCardData {
  productActivations: ProductActivation[];
  workOrders: FulfillmentOrder[];
}

@Component({
  selector: 'orders-edit-order',
  templateUrl: './edit-order.component.html',
  styleUrls: [
    '../../shared/shared-styles.scss',
    '../create-order/create/create-order.component.scss',
    './edit-order.component.scss',
  ],
  providers: [OrderPermissionsService],
  standalone: false,
})
export class EditOrderComponent implements OnInit, OnDestroy {
  @Input() business?: Pick<AccountGroup, 'externalIdentifiers' | 'napData' | 'accountGroupId'>;
  @Input() users: User[] = [];
  @Input() fileUploadUrl = '';
  @Input() productIds: string[] = [];
  @Input() fulfillmentFormLink = '';
  @Input() hideNotesSections?: boolean;
  @Input() hideTagsSection?: boolean;
  @Input() hideAttachmentsSection?: boolean;
  @Input() hideOrderFormSaveButton?: boolean;

  @Input() set order(order: Order) {
    this.order$$.next(order);
  }

  // let the parent component (in the client) be responsible for refreshing the order
  // so that other child components on the page respond accordingly
  @Output() refreshOrderDetails = new EventEmitter<boolean>();

  @ViewChild('lineItems') lineItemsComponent!: LineItemsComponent;

  productPrereqForm$$ = new BehaviorSubject<ProductActivationPrereqFormComponent | null>(null);
  productPrereqForm$ = this.productPrereqForm$$.asObservable().pipe(
    filter((form) => form != null),
    // delay is needed due to lifecycle issues with ViewChild components and observable initialization
    delay(0),
  );

  get productPrereqForm(): ProductActivationPrereqFormComponent | null {
    return this.productPrereqForm$$.getValue();
  }

  @ViewChild('productPrereqForm', { static: false }) set productPrereqForm(form: ProductActivationPrereqFormComponent) {
    if (form) {
      this.productPrereqForm$$.next(form);
    }
  }

  salesOrderForm$$ = new BehaviorSubject<OrderFormComponent | null>(null);
  salesOrderForm$ = this.salesOrderForm$$.asObservable().pipe(
    filter((form) => form != null),
    // delay is needed due to lifecycle issues with ViewChild components and observable initialization
    delay(0),
  );

  get salesOrderForm(): OrderFormComponent | null {
    return this.salesOrderForm$$.getValue();
  }

  @ViewChild('orderForm', { static: false }) set salesOrderForm(form: OrderFormComponent) {
    if (form) {
      this.salesOrderForm$$.next(form);
    }
  }

  @ViewChild('workOrderDetails') workOrderDetails: WorkOrderDetailsComponent;

  public Scheduled: Status = Status.SCHEDULED_ACTIVATION;

  private readonly order$$ = new BehaviorSubject<Order | null>(null);
  order$: Observable<Order> = this.order$$.asObservable().pipe(filter((order) => order != null));
  workOrders$!: Observable<FulfillmentOrder[]>;
  productActivations$!: Observable<ProductActivation[]>;
  fulfillmentStatusCardData$: Observable<fulfillmentStatusCardData>;
  protected lineItems$: Observable<LineItem[]>;

  orderFormOptions$!: Observable<OrderFormOptionsInterface>;

  orderDetailsFormGroup: UntypedFormGroup;
  orderFormGroup: UntypedFormGroup;
  subscriptions: Subscription[] = [];
  attachmentsUploadUrl = PROD_SSC_UPLOAD_URL;
  orderConfig$!: Observable<ConfigInterface>;
  hasAtLeastOneForm$!: Observable<boolean>;
  shouldShowDetailsNeededBanner$!: Observable<boolean>;
  unifiedOrderPageEnabled$!: Observable<boolean>;
  selectedTabIndex$: Observable<number>;
  hasOrderForms$: Observable<boolean>;
  invalidForms$!: Observable<boolean>;
  showFooter$: Observable<boolean>;
  workOrderPersona$!: Observable<WorkOrderPersona>;
  protected readonly WorkOrderPersona = WorkOrderPersona;
  public Status = Status;

  partnerCurrency$: Observable<string>;
  variablePricesMap$: Observable<AppVariablePriceMap>;
  variablePackagePricesMap$: Observable<PackageVariablePriceMap>;

  canEditOrderContents$: Observable<boolean>;
  canSeeWholesaleColumn$: Observable<boolean>;
  canEditInvoices$: Observable<boolean>;
  canViewFulfillmentStatuses$: Observable<boolean>;
  canViewTags$: Observable<boolean>;
  canEditTags$: Observable<boolean>;
  canViewCreateOrderDetails$: Observable<boolean>;
  canViewActiveItems$: Observable<boolean>;
  canViewInvalidLineItemsBanner$: Observable<boolean>;

  canSubmitWithoutRequiredFields$: Observable<boolean>;
  canReviewCancellationRequest$: Observable<boolean>;
  canCancelOrder$: Observable<boolean>;
  canArchiveOrder$: Observable<boolean>;
  canActivateOrder$: Observable<boolean>;
  canScheduleActivation$: Observable<boolean>;
  canReviewSubmittedOrder$: Observable<boolean>;
  canChargeSmbOnOrderSubmission$: Observable<boolean>;

  projectTrackerInfo$: Observable<ProjectTrackerInfo[]>;
  retailSummary$: Observable<RetailSummary>;

  godaddyApp$: Observable<App | null> = of(null);

  confirmationCheckbox = false;
  invalidActivateAttempt = false;

  canEditOrderCurrency$: Observable<boolean>;
  private readonly currencyOverride$$ = new BehaviorSubject<string>('');
  currencyOverride$ = this.currencyOverride$$.asObservable();
  currencySelectorOptions: string[] = [Currency.USD, Currency.CAD, Currency.AUD];
  invalidOrderLineItems$: Observable<ValidationErrorCodes[]>;

  protected processingAction = signal(false);
  saveUpdatedLineItems$$ = new Subject<LineItem[]>();
  saveUpdatedWholesaleSummary$$: Subject<WholesaleSummary> = new Subject<WholesaleSummary>();
  protected wholesaleSummary$: Observable<WholesaleSummary> = this.saveUpdatedWholesaleSummary$$
    .asObservable()
    .pipe(distinctUntilChanged());
  protected canManageMarketplace$: Observable<boolean>;
  constructor(
    @Inject(PAGE_ORDER_CONFIG_TOKEN) private readonly pageOrderConfig: PageOrderConfig,
    private environmentService: EnvironmentService,
    private formBuilder: UntypedFormBuilder,
    private salesOrdersService: SalesOrdersService,
    private workOrderFormConfigService: WorkOrderFormConfigService,
    private workOrderSalesOrderSummaryService: WorkOrderSalesOrderSummaryService,
    private billingService: BillingService,
    private variablePricesService: VariablePricesService,
    private readonly permissionsService: OrderPermissionsService,
    private dialog: MatDialog,
    private confirmationModal: OpenConfirmationModalService,
    private featureFlagService: FeatureFlagService,
    private analyticsService: ProductAnalyticsService,
    private snackbarService: SnackbarService,
    private destroyRef: DestroyRef,
    private readonly translateService: TranslateService,
    private projectTrackerService: ProjectsAndSubtasksByProductsService,
    private lineItemValidationService: OrderLineItemValidationService,
    private tabService: OrdersEditTabService,
    private formValidationService: OrdersEditOrderFormValidationService,
    private packageService: PackageService,
    private appsService: AppPartnerService,
  ) {
    this.orderDetailsFormGroup = this.formBuilder.group({});
    this.orderFormGroup = this.formBuilder.group({});
  }

  ngOnInit(): void {
    this.selectedTabIndex$ = this.tabService.selectedTabIndex$;
    if (this.environmentService.getEnvironment() === Environment.DEMO) {
      this.attachmentsUploadUrl = DEMO_SSC_UPLOAD_URL;
    }
    if (this.business) {
      this.unifiedOrderPageEnabled$ = this.featureFlagService
        .batchGetStatus(this.business.externalIdentifiers.partnerId, this.business.accountGroupId, [
          Features.PC_UNIFIED_ORDERS_PAGE,
        ])
        .pipe(map((res) => res[Features.PC_UNIFIED_ORDERS_PAGE]));

      this.orderConfig$ = this.salesOrdersService
        .getConfig(this.business.externalIdentifiers.partnerId, this.business.externalIdentifiers.marketId)
        .pipe(shareReplay(1));
    }

    this.orderFormOptions$ = combineLatest([
      this.orderConfig$,
      this.permissionsService.CanEdit(OrderFeature.OrderForms),
    ]).pipe(
      map(([config, canEditOrderForms]) => {
        return {
          readOnly: !canEditOrderForms,
          showOfficeUseQuestions: true,
          bypassRequiredQuestions: !config.salespersonOptions?.validateRequiredFields,
        };
      }),
    );

    const formConfigs$: Observable<Map<string, FulfillmentFormConfigInterface>> = this.order$.pipe(
      switchMap((order) => {
        return this.salesOrdersService.previewOrderActivations(order.orderId, order.businessId).pipe(
          map((activations) => {
            const productIds: string[] = activations?.map((activation) => activation.productId) || [];
            return Array.from(new Set(productIds));
          }),
          switchMap((productIds) => {
            const formConfigs = productIds.map((productId) =>
              this.workOrderFormConfigService.getFormConfig(order.orderId, productId, order.businessId),
            );
            return combineLatest(formConfigs).pipe(
              map((configs) => {
                const productIdToConfigMap: Map<string, FulfillmentFormConfigInterface> = new Map();
                productIds.forEach((productId, i) => {
                  productIdToConfigMap.set(productId, configs[i]);
                });
                return productIdToConfigMap;
              }),
            );
          }),
        );
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

    this.hasAtLeastOneForm$ = formConfigs$.pipe(
      map((formConfigs) => {
        return Array.from(formConfigs.values()).some((config) => {
          if (!config || !config.orderForm) {
            return false;
          }
          return config?.orderForm?.length > 0;
        });
      }),
    );

    this.workOrders$ = this.order$.pipe(
      switchMap((order) =>
        this.workOrderSalesOrderSummaryService.getWorkOrdersForSalesOrder(order.orderId, order.businessId, true),
      ),
      map((workOrders) => {
        if (!workOrders) {
          return [];
        }
        return workOrders.filter(
          (workOrder) => Object.hasOwnProperty.call(workOrder, 'status') && workOrder?.fulfillmentFormInfo?.hasForm,
        );
      }),
    );
    this.workOrderPersona$ = this.pageOrderConfig.orderPermissions$.pipe(
      map((permissions) => {
        let persona = WorkOrderPersona.SALES;
        if (permissions.accessManageOrders) {
          persona = WorkOrderPersona.CP;
        }
        return persona;
      }),
    );
    this.canEditOrderContents$ = this.permissionsService.CanEdit(OrderFeature.OrderContents);
    this.canSeeWholesaleColumn$ = this.permissionsService.CanEdit(OrderFeature.WholesalePriceColumn);
    this.canEditInvoices$ = this.permissionsService.CanEdit(OrderFeature.Invoices);
    this.canViewFulfillmentStatuses$ = this.permissionsService.CanView(OrderFeature.FulfillmentStatuses);
    this.canViewTags$ = this.permissionsService
      .CanView(OrderFeature.Tags)
      .pipe(map((canView) => canView && !this.hideTagsSection));
    this.canEditTags$ = this.permissionsService.CanEdit(OrderFeature.Tags);
    this.canViewCreateOrderDetails$ = this.permissionsService.CanView(OrderFeature.CreateOrderDetails);
    this.canViewActiveItems$ = this.permissionsService.CanView(OrderFeature.ActiveItems);
    this.canViewInvalidLineItemsBanner$ = this.permissionsService.CanView(OrderFeature.InvalidLineItemsBanner);

    this.canSubmitWithoutRequiredFields$ = this.permissionsService.CanDoAction(OrderAction.SubmitWithoutRequiredFields);
    this.canReviewCancellationRequest$ = this.permissionsService.CanDoAction(OrderAction.ReviewCancellationRequest);
    this.canCancelOrder$ = this.permissionsService.CanDoAction(OrderAction.CancelOrder);
    this.canArchiveOrder$ = this.permissionsService.CanDoAction(OrderAction.ArchiveOrder);
    this.canActivateOrder$ = this.permissionsService.CanDoAction(OrderAction.ActivateOrder);
    this.canScheduleActivation$ = this.permissionsService.CanDoAction(OrderAction.ScheduleActivation);
    this.canReviewSubmittedOrder$ = this.permissionsService.CanDoAction(OrderAction.ReviewSubmittedOrder);
    this.canEditOrderCurrency$ = this.permissionsService.CanDoAction(OrderAction.SelectOrderCurrency);
    this.canChargeSmbOnOrderSubmission$ = this.permissionsService.CanDoAction(OrderAction.ChargeSMBOnOrderSubmission);
    this.canManageMarketplace$ = this.permissionsService.canManageMarketplace$;
    this.showFooter$ = combineLatest([
      this.unifiedOrderPageEnabled$,
      this.canCancelOrder$,
      this.canArchiveOrder$,
      this.canActivateOrder$,
      this.canScheduleActivation$,
      this.canReviewSubmittedOrder$,
    ]).pipe(
      map(
        ([
          unifiedOrderPageEnabled,
          canCancelOrder,
          canArchiveOrder,
          canActivateOrder,
          canScheduleActivation,
          canReviewSubmittedOrder,
        ]) =>
          unifiedOrderPageEnabled &&
          (canCancelOrder || canArchiveOrder || canActivateOrder || canScheduleActivation || canReviewSubmittedOrder),
      ),
    );

    this.shouldShowDetailsNeededBanner$ = combineLatest([this.workOrders$, this.unifiedOrderPageEnabled$]).pipe(
      map(
        ([workOrders, isUnifiedOrderPageEnabled]) =>
          !isUnifiedOrderPageEnabled &&
          workOrders.some((wo) => wo.status === FulfillmentOrderStatus.FULFILLMENT_ORDER_STATUS_DETAILS_NEEDED),
      ),
    );

    this.invalidForms$ = combineLatest([
      this.productPrereqForm$,
      this.salesOrderForm$,
      this.unifiedOrderPageEnabled$,
    ]).pipe(
      switchMap(([prereqForm, salesOrderForm, unifiedOrderPageEnabled]) => {
        if (!prereqForm || !salesOrderForm || !unifiedOrderPageEnabled) {
          return of(false);
        }
        return combineLatest([prereqForm.isValid$, salesOrderForm.isValid$]).pipe(
          map(([prereqFormValid, orderFormValid]) => {
            return !prereqFormValid || !orderFormValid;
          }),
        );
      }),
      tap((invalidForms) => this.formValidationService.setInvalidForms(invalidForms)),
      distinctUntilChanged(),
      startWith(false),
    );

    this.hasOrderForms$ = combineLatest([this.productPrereqForm$, this.salesOrderForm$]).pipe(
      filter(([productPrereqForm, salesOrderForm]) => productPrereqForm !== null && salesOrderForm !== null),
      switchMap(([productPrereqForm, salesOrderForm]) => {
        return combineLatest([productPrereqForm.hasRestrictions$, salesOrderForm.orderData$]);
      }),
      withLatestFrom(this.unifiedOrderPageEnabled$),
      map(([[businessPrereqRequired, orderData], unifiedOrderPageEnabled]) => {
        if (!unifiedOrderPageEnabled) {
          return false;
        }
        return businessPrereqRequired || orderData?.orderForms?.length > 0 || orderData?.extraQuestions?.length > 0;
      }),
    );

    this.subscriptions.push(
      this.hasAtLeastOneForm$.pipe(take(1)).subscribe((hasFormConfig) => {
        if (hasFormConfig) {
          this.analyticsService.trackEvent('draft-order-viewed', 'page-view', 'page-view');
        }
      }),
    );

    this.order$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((order) => this.variablePricesService.setLineItems(order.lineItems));
    this.partnerCurrency$ = this.order$.pipe(
      switchMap((order) => {
        return this.billingService.getMerchantWholesaleCurrency(order.partnerId);
      }),
      shareReplay(1),
    );
    this.variablePricesMap$ = this.variablePricesService.variablePricesMap$;
    this.variablePackagePricesMap$ = this.variablePricesService.variablePackagePricesMap$;

    this.retailSummary$ = this.order$.pipe(
      map((salesOrder) => {
        return getRetailSummaryFromOrder(salesOrder);
      }),
    );

    const orderCurrencyInitialChange$ = combineLatest([this.order$, this.canEditOrderCurrency$]).pipe(
      take(1),
      map(([order, canEditOrderCurrency]) => {
        if (canEditOrderCurrency && order?.lineItems?.length) {
          return order.lineItems[0].currencyCode;
        }
      }),
    );

    this.currencyOverride$ = merge(this.currencyOverride$$.asObservable(), orderCurrencyInitialChange$);

    this.projectTrackerInfo$ = this.order$.pipe(
      switchMap((salesOrder) =>
        this.projectTrackerService.lookupVisibleProjectsForAccountByOrderIds(
          salesOrder.partnerId,
          salesOrder.businessId,
          salesOrder.orderId ? [salesOrder.orderId] : [],
          [PersonaType.partner, PersonaType.sales_person, PersonaType.smb],
        ),
      ),
      map((info: ProjectTrackerInfo[]) =>
        info.map((tracker) => {
          return {
            ...tracker,
            subtasks: tracker.subtasks,
          };
        }),
      ),
    );

    this.productActivations$ = this.order$.pipe(
      switchMap((order) => {
        if (order.productActivations) {
          return of(order.productActivations);
        }
        return this.salesOrdersService.previewOrderActivations(order.orderId, order.businessId);
      }),
      catchError(() => {
        return of([]);
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

    this.fulfillmentStatusCardData$ = combineLatest([
      this.productActivations$,
      this.canViewFulfillmentStatuses$,
      this.workOrders$,
    ]).pipe(
      takeUntilDestroyed(this.destroyRef),
      map(([productActivations, canViewFulfillmentStatuses, workOrders]) => {
        const shouldDisplay = productActivations && productActivations.length > 0 && canViewFulfillmentStatuses;

        if (shouldDisplay) {
          return {
            productActivations,
            workOrders,
          };
        } else {
          return null;
        }
      }),
    );
    this.invalidOrderLineItems$ = this.lineItemValidationService.invalidLineItemErrors$.pipe(
      map((errors) => errors?.errorCodes),
    );

    this.saveUpdatedLineItems$$
      .asObservable()
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        withLatestFrom(this.order$),
        debounceTime(400),
        tap(() => this.processingAction.set(true)),
        mergeMap(([lineItems, order]) => {
          return this.salesOrdersService
            .updateLineItems(
              order.orderId,
              order.businessId,
              lineItems ?? this.lineItemsComponent.getCurrentLineItems(),
            )
            .pipe(catchError(() => EMPTY));
        }),
      )
      .subscribe({
        next: () => {
          this.processingAction.set(false);
          this.refreshOrder();
          this.workOrderDetails?.refreshWorkOrders();
        },
      });

    this.lineItems$ = this.order$.pipe(
      distinctUntilChanged(),
      map((order) => order.lineItems),
    );

    const godaddyItemIdInPackage$ = this.lineItems$.pipe(
      switchMap((lineItems) => {
        const packageIds = lineItems?.filter((item) => !!item?.packageId).map((item) => item.packageId);
        if (packageIds && packageIds.length > 0) {
          return this.packageService.getMulti(packageIds);
        }
        return of([]);
      }),
      map((packages) => {
        return [].concat(
          ...packages.map(
            (pkg) =>
              pkg?.lineItems?.lineItems
                ?.filter((item) => DynamicOrderFormAppIds.indexOf(item?.id) !== -1)
                .map((item) => item.id) || [],
          ),
        );
      }),
      map((itemIds) => itemIds[0]),
    );

    this.godaddyApp$ = combineLatest(godaddyItemIdInPackage$, this.order$).pipe(
      switchMap(([godaddyItemIdInPackage, order]) => {
        if (godaddyItemIdInPackage) {
          return this.appsService.getMulti(
            [new AppKey({ appId: godaddyItemIdInPackage })],
            order.partnerId,
            order.marketId,
            null,
            false,
          );
        }
        if (!order.lineItems || order.lineItems.length === 0) {
          return of([]);
        }

        const lineItem = order.lineItems.find((item) => DynamicOrderFormAppIds.indexOf(item?.appKey?.appId) !== -1);
        if (lineItem) {
          return this.appsService.getMulti(
            [new AppKey({ appId: lineItem?.appKey?.appId })],
            order.partnerId,
            order.marketId,
            null,
            false,
          );
        }
        return of([]);
      }),
      map((apps: App[]) => apps[0] || null),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => {
      sub.unsubscribe();
    });
  }

  hasUnsavedChanges(): Observable<boolean> {
    return this.productPrereqForm?.hasUnsavedChanges$ ?? of(false);
  }

  navigateToWorkOrdersTab(): void {
    this.tabService.navigateToWorkOrdersTab();
  }

  indexChanged(index: number): void {
    this.tabService.setSelectedTabIndex(index);
  }

  validateForm(): Observable<boolean> {
    if (!this.productPrereqForm) {
      return of(false);
    }

    return combineLatest([
      this.permissionsService.CanDoAction(OrderAction.SubmitWithoutRequiredFields),
      this.productPrereqForm.isValid$,
      this.unifiedOrderPageEnabled$,
    ]).pipe(
      map(([canSubmitWithoutRequiredFields, isValid, unifiedOrderPageEnabled]) => {
        if (!isValid) {
          this.snackbarService.openErrorSnack('LIB_ORDERS.SALES_ORDERS.ERRORS.PREREQ_FORM_IS_NOT_VALID');
          return false;
        }
        if (!canSubmitWithoutRequiredFields && !this.salesOrderForm?.validateForm()) {
          if (unifiedOrderPageEnabled) {
            this.confirmationModal
              .openModal({
                title: 'LIB_ORDERS.SALES_ORDERS.INVALID_ORDER_FORMS_DIALOG.TITLE',
                message: 'LIB_ORDERS.SALES_ORDERS.INVALID_ORDER_FORMS_DIALOG.MESSAGE',
                confirmButtonText: 'LIB_ORDERS.SALES_ORDERS.INVALID_ORDER_FORMS_DIALOG.GO_TO_FORMS',
                cancelButtonText: 'LIB_ORDERS.COMMON.ACTION_LABELS.CLOSE',
                cancelOnEscapeKeyOrBackgroundClick: true,
              })
              .subscribe((goToForms) => {
                if (goToForms) {
                  this.tabService.navigateToOrderFormsTab();
                }
              });
          } else {
            this.snackbarService.openErrorSnack('LIB_ORDERS.SALES_ORDERS.ERRORS.FORM_IS_NOT_VALID');
          }
          return false;
        }
        return true;
      }),
    );
  }

  saveOrderFormIfDirty(): Observable<boolean> {
    if (this.orderFormGroup.dirty && this.salesOrderForm) {
      return this.salesOrderForm.updateAnswers();
    }
    return of(true);
  }

  // updates the line items on the order and triggers the parent component to update the order reference
  saveUpdatedLineItems(lineItems?: LineItem[]): void {
    this.saveUpdatedLineItems$$.next(lineItems);
  }

  /**
   * returns the data from common form fields in a format that is usable by sales orders
   */
  public getCommonFormData(): CommonField[] {
    return this.salesOrderForm?.getCommonFormData() ?? [];
  }

  /**
   * returns the data from custom forms in a format that is usable by sales orders
   */
  public getCustomFormData(): CustomField[] {
    return this.salesOrderForm?.getCustomFormData() ?? [];
  }

  /**
   * returns the data from extra fields in a format that is usable by sales orders
   */
  public getExtraFieldsData(): Field[] {
    return this.salesOrderForm?.getExtraFieldsData() ?? [];
  }

  public isValid(): Observable<boolean> {
    return this.productPrereqForm?.isValid$ ?? of(false);
  }

  public printPage(): void {
    this.salesOrderForm?.openDropdown();
  }

  public openOrderFormTab(): void {
    this.tabService.navigateToOrderFormsTab;
  }

  variablePriceChanged(event: { appId: string; customPrice: number; packageId?: string }) {
    const lineItems = this.lineItemsComponent.getCurrentLineItems();
    applyVariablePriceChange(event, lineItems);
    this.saveUpdatedLineItems(lineItems);
  }

  orderCharged(orderCharged: boolean): void {
    if (orderCharged) {
      this.refreshOrder();
    }
  }

  approveSubmittedOrder(): void {
    this.processingAction.set(true);

    const dialogTitle = this.translateService.instant(
      'LIB_ORDERS.COMMON.ORDERS.DIALOGS.APPROVE_SUBMITTED_ORDER.TITLE',
      {
        businessName: this.businessName(),
      },
    );

    this.lineItemValidationService
      .areBillingTermsValidOrOpenModal$()
      .pipe(
        take(1),
        switchMap((areBillingTermsValid) => {
          if (areBillingTermsValid) {
            return this.confirmationModal.openModal({
              title: dialogTitle,
              message: 'LIB_ORDERS.COMMON.ORDERS.DIALOGS.APPROVE_SUBMITTED_ORDER.MESSAGE',
              confirmButtonText: 'LIB_ORDERS.COMMON.ORDERS.DIALOGS.APPROVE_SUBMITTED_ORDER.CONFIRM',
              cancelButtonText: 'LIB_ORDERS.COMMON.ORDERS.DIALOGS.APPROVE_SUBMITTED_ORDER.CANCEL',
              type: 'confirm',
            });
          }
          return of(false);
        }),
        withLatestFrom(this.order$),
        switchMap(([confirm, order]) => {
          if (!confirm) {
            return of(null);
          }
          return this.salesOrdersService.approve(order.businessId, order.orderId);
        }),
      )
      .subscribe({
        next: (result) => {
          if (!result) {
            return;
          }

          this.refreshOrder();
          this.snackbarService.openSuccessSnack(
            'LIB_ORDERS.COMMON.ORDERS.DIALOGS.APPROVE_SUBMITTED_ORDER.SUCCESS_MESSAGE',
          );
        },
        error: () => {
          this.snackbarService.openErrorSnack('LIB_ORDERS.COMMON.ORDERS.DIALOGS.APPROVE_SUBMITTED_ORDER.ERROR_MESSAGE');
        },
        complete: () => {
          this.processingAction.set(false);
        },
      });
  }

  declineSubmittedOrder(): void {
    this.processingAction.set(true);

    this.subscriptions.push(
      this.order$
        .pipe(
          take(1),
          switchMap((order) => {
            const declineDialogRef = this.dialog.open(OrderDeclineDialogComponent, {
              width: ORDER_DETAIL_DIALOG_WIDTH,
              maxHeight: ORDER_DETAIL_DIALOG_HEIGHT,
            });
            declineDialogRef.componentInstance.orderId = order.orderId;
            declineDialogRef.componentInstance.businessId = order.businessId;
            declineDialogRef.componentInstance.partnerId = order.partnerId;
            declineDialogRef.componentInstance.title = this.translateService.instant(
              'LIB_ORDERS.COMMON.ORDERS.DIALOGS.DECLINE_SUBMITTED_ORDER.TITLE',
              {
                businessName: this.businessName(),
              },
            );
            declineDialogRef.componentInstance.details = this.translateService.instant(
              'LIB_ORDERS.COMMON.ORDERS.DIALOGS.DECLINE_SUBMITTED_ORDER.MESSAGE',
            );

            return declineDialogRef.afterClosed();
          }),
        )
        .subscribe({
          next: (result) => {
            if (!result) {
              return;
            }
            this.refreshOrder();
            this.snackbarService.openSuccessSnack(
              'LIB_ORDERS.COMMON.ORDERS.DIALOGS.DECLINE_SUBMITTED_ORDER.SUCCESS_MESSAGE',
            );
          },
          error: () => {
            this.snackbarService.openErrorSnack(
              'LIB_ORDERS.COMMON.ORDERS.DIALOGS.DECLINE_SUBMITTED_ORDER.ERROR_MESSAGE',
            );
          },
          complete: () => {
            this.processingAction.set(false);
          },
        }),
    );
  }

  onRequestToCancel(): void {
    this.processingAction.set(true);
    this.dialog
      .open(CancelOrderDialogComponent, {
        width: ConfirmationModalWidth,
        maxWidth: ConfirmationModalMaxWidth,
        autoFocus: false,
        data: {
          isAdmin: true,
        },
      })
      .afterClosed()
      .pipe(
        withLatestFrom(this.order$),
        switchMap(([cancellationReason, order]) => {
          if (!cancellationReason) {
            return of(null);
          }

          return this.salesOrdersService.cancelOrder(order.orderId, order.businessId, cancellationReason);
        }),
      )
      .subscribe({
        next: (result) => {
          this.processingAction.set(false);
          if (!result) {
            return;
          }

          this.refreshOrder();
          this.snackbarService.openSuccessSnack(
            'LIB_ORDERS.COMMON.ORDERS.DIALOGS.REQUEST_TO_CANCEL_ORDER.SUCCESS_MESSAGE',
          );
        },
        error: () => {
          this.processingAction.set(false);
          this.snackbarService.openErrorSnack('LIB_ORDERS.COMMON.ORDERS.DIALOGS.REQUEST_TO_CANCEL_ORDER.ERROR_MESSAGE');
        },
      });
  }

  approveCancellationRequest(): void {
    this.processingAction.set(true);

    const getDialogTranslationKey = (path: string) => {
      const base = 'LIB_ORDERS.COMMON.ORDERS.DIALOGS.APPROVE_CANCELLATION_REQUEST';
      return `${base}.${path}`;
    };

    const confirmCancellation$ = this.confirmationModal.openModal({
      title: this.translateService.instant(getDialogTranslationKey('TITLE')),
      message: this.translateService.instant(getDialogTranslationKey('MESSAGE')),
      confirmButtonText: this.translateService.instant(getDialogTranslationKey('CONFIRM')),
      cancelButtonText: this.translateService.instant(getDialogTranslationKey('CANCEL')),
      type: 'warn',
    });

    combineLatest([this.order$, confirmCancellation$])
      .pipe(
        switchMap(([order, confirmCancellation]) => {
          if (!confirmCancellation) {
            return of(false);
          }

          return this.salesOrdersService.approveCancellation(order.orderId, order.businessId);
        }),
        take(1),
      )
      .subscribe({
        next: (result) => {
          if (!result) {
            return;
          }

          this.refreshOrder();
          this.snackbarService.openSuccessSnack(getDialogTranslationKey('SUCCESS_MESSAGE'));
        },
        error: () => {
          this.snackbarService.openErrorSnack(getDialogTranslationKey('ERROR_MESSAGE'));
        },
        complete: () => {
          this.processingAction.set(false);
        },
      });
  }

  onDeclineCancellationRequest(): void {
    this.processingAction.set(true);
    this.dialog
      .open(DeclineCancellationDialogComponent, {
        width: ConfirmationModalWidth,
        maxWidth: ConfirmationModalMaxWidth,
        autoFocus: false,
      })
      .afterClosed()
      .pipe(
        withLatestFrom(this.order$),
        switchMap(([notes, order]) => {
          if (!notes) {
            return of(null);
          }

          return this.salesOrdersService.declineCancellation(order.orderId, order.businessId, notes);
        }),
      )
      .subscribe({
        next: (result) => {
          if (!result) {
            return;
          }
          this.refreshOrder();
          this.snackbarService.openSuccessSnack(
            'LIB_ORDERS.COMMON.ORDERS.DIALOGS.DECLINE_ORDER_CANCELLATION.SUCCESS_MESSAGE',
          );
        },
        error: () => {
          this.snackbarService.openErrorSnack(
            'LIB_ORDERS.COMMON.ORDERS.DIALOGS.DECLINE_ORDER_CANCELLATION.ERROR_MESSAGE',
          );
        },
        complete: () => {
          this.processingAction.set(false);
        },
      });
  }

  onArchive(): void {
    this.processingAction.set(true);
    this.confirmationModal
      .openModal({
        title: this.translateService.instant('LIB_ORDERS.COMMON.ORDERS.DIALOGS.ARCHIVE_ORDER.TITLE'),
        message: this.translateService.instant('LIB_ORDERS.COMMON.ORDERS.DIALOGS.ARCHIVE_ORDER.MESSAGE'),
        confirmButtonText: this.translateService.instant('LIB_ORDERS.COMMON.ORDERS.DIALOGS.ARCHIVE_ORDER.ARCHIVE'),
        cancelButtonText: this.translateService.instant('LIB_ORDERS.COMMON.ACTION_LABELS.CANCEL'),
        type: 'warn',
      })
      .pipe(
        withLatestFrom(this.order$),
        switchMap(([archive, order]) => {
          if (!archive) {
            return of(null);
          }

          return this.salesOrdersService.archive(order.businessId, order.orderId);
        }),
      )
      .subscribe({
        next: (archivedOrder) => {
          this.processingAction.set(false);
          if (!archivedOrder) {
            return;
          }

          const message = this.translateService.instant(
            'LIB_ORDERS.COMMON.ORDERS.DIALOGS.ARCHIVE_ORDER.SUCCESS_MESSAGE',
          );
          this.snackbarService.openSuccessSnack(message);
          this.refreshOrder();
        },
        error: () => {
          this.processingAction.set(false);
          const message = this.translateService.instant('LIB_ORDERS.COMMON.ORDERS.DIALOGS.ARCHIVE_ORDER.ERROR_MESSAGE');
          this.snackbarService.openErrorSnack(message);
        },
      });
  }

  onActivateBtnPressed(order: Order): void {
    if (this.processingAction()) return;

    combineLatest([
      this.proceedWithoutUnsavedChanges$(),
      this.lineItemValidationService.areBillingTermsValidOrOpenModal$(),
    ])
      .pipe(
        switchMap(([proceed, areBillingTermsValid]) => {
          if (!proceed || !areBillingTermsValid) return EMPTY;
          this.processingAction.set(true);
          return this.lineItemValidationService.canOrderBeWholesaleBilledOrOpenModal$();
        }),
        switchMap((canOrderBeWholesaleBilled) => {
          if (!canOrderBeWholesaleBilled) {
            return EMPTY;
          }
          this.processingAction.set(true);
          return combineLatest([this.salesOrderForm.isValid$, this.productPrereqForm.isValid$]);
        }),
        take(1),
      )
      .subscribe(([salesOrderFormIsValid, prereqFormIsValid]) => {
        if (!salesOrderFormIsValid || !prereqFormIsValid) {
          this.invalidActivateAttempt = true;
          this.processingAction.set(false);
          this.snackbarService.openErrorSnack('LIB_ORDERS.SALES_ORDERS.ERRORS.FORM_IS_NOT_VALID');
        } else {
          this.activateProducts(order);
        }
      });
  }

  onScheduleBtnPressed(order: Order): void {
    if (this.processingAction() || !this.confirmationCheckbox) return;

    combineLatest([
      this.proceedWithoutUnsavedChanges$(),
      this.lineItemValidationService.areBillingTermsValidOrOpenModal$(),
    ])
      .pipe(
        switchMap(([proceed, areBillingTermsValid]) => {
          if (!proceed || !areBillingTermsValid) return EMPTY;
          this.processingAction.set(true);
          return this.lineItemValidationService.canOrderBeWholesaleBilledOrOpenModal$();
        }),
        switchMap((canOrderBeWholesaleBilled) => {
          if (!canOrderBeWholesaleBilled) {
            return EMPTY;
          }
          this.processingAction.set(true);
          return combineLatest([this.salesOrderForm.isValid$, this.productPrereqForm.isValid$]);
        }),
        take(1),
      )
      .subscribe(([salesOrderFormIsValid, prereqFormIsValid]) => {
        if (!salesOrderFormIsValid || !prereqFormIsValid) {
          this.invalidActivateAttempt = true;
          this.processingAction.set(false);
          this.snackbarService.openErrorSnack('LIB_ORDERS.SALES_ORDERS.ERRORS.FORM_IS_NOT_VALID');
        } else {
          this.scheduleActivation(order);
        }
      });
  }

  private activateProducts(order: Order): void {
    const activateSalesOrder$ = this.activateSalesOrder(order);

    this.checkDateForActivation(true)
      .pipe(
        switchMap((shouldContinue) => {
          if (shouldContinue) {
            return activateSalesOrder$;
          }

          return of(null);
        }),
        take(1),
      )
      .subscribe({
        next: (processingOrder: Order) => {
          if (processingOrder !== null) {
            this.snackbarService.openSuccessSnack('LIB_ORDERS.SALES_ORDERS.SUCCESS.ACTIVATION_IN_PROCESS');
            this.refreshOrder();
            this.refreshOrderWhileProcessing();
          }
        },
        error: (err) => {
          if (err.message === INVALID_APPS_IN_ORDER) {
            this.snackbarService.openErrorSnack('LIB_ORDERS.SALES_ORDERS.ERRORS.INVALID_PRODUCTS_ERROR');
          } else {
            console.error(err);
            this.snackbarService.openErrorSnack('LIB_ORDERS.SALES_ORDERS.ERRORS.GENERIC_ERROR_TRY_AGAIN');
          }
        },
        complete: () => {
          this.processingAction.set(false);
        },
      });
  }

  private scheduleActivation(order: Order): void {
    const scheduleSalesOrder$ = this.scheduleSalesOrder(order);

    this.checkDateForActivation(false)
      .pipe(
        switchMap((shouldContinue) => {
          if (shouldContinue) {
            return scheduleSalesOrder$;
          }

          return of(null);
        }),
        take(1),
      )
      .subscribe({
        next: (scheduledOrder) => {
          if (scheduledOrder !== null) {
            this.snackbarService.openSuccessSnack('LIB_ORDERS.SALES_ORDERS.SUCCESS.ORDER_SCHEDULED');
            this.refreshOrder();
          }
        },
        error: (err) => {
          if (err.message === INVALID_APPS_IN_ORDER) {
            this.snackbarService.openErrorSnack('LIB_ORDERS.SALES_ORDERS.ERRORS.INVALID_PRODUCTS_ERROR');
          } else {
            console.error(err);
            this.snackbarService.openErrorSnack('LIB_ORDERS.SALES_ORDERS.ERRORS.GENERIC_ERROR_TRY_AGAIN');
          }
        },
        complete: () => this.processingAction.set(false),
      });
  }

  private activateSalesOrder(order: Order): Observable<Order> {
    return this.salesOrdersService.activate(
      order.orderId,
      order.businessId,
      this.getCustomFormData(),
      this.getCommonFormData(),
      this.getExtraFieldsData(),
    );
  }

  private scheduleSalesOrder(order: Order): Observable<Order> {
    return this.salesOrdersService
      .scheduleActivation(
        order.orderId,
        order.businessId,
        this.getCustomFormData(),
        this.getCommonFormData(),
        this.getExtraFieldsData(),
      )
      .pipe(switchMap(() => this.salesOrdersService.get(order.orderId, order.businessId)));
  }

  private checkDateForActivation(activatingNow: boolean): Observable<boolean> {
    return this.order$.pipe(
      take(1),
      switchMap((order) => {
        const todayDate = new Date(new Date().setHours(0, 0, 0, 0));
        const orderDate = new Date(order.requestedActivation);

        const dateCheck = activatingNow ? !isSameDay(orderDate, todayDate) : isBefore(orderDate, todayDate);

        if (dateCheck) {
          const activationDateDialog = this.dialog.open(ActivationDateConfirmationDialogComponent, {
            width: ConfirmationModalWidth,
            maxWidth: ConfirmationModalMaxWidth,
            autoFocus: false,
            data: {
              orderId: order.orderId,
              businessId: order.businessId,
              orderDate: format(orderDate, 'LLL dd, yyyy'),
              todayDate: format(todayDate, 'LLL dd, yyyy'),
            },
          });

          return activationDateDialog.afterClosed();
        }

        return of(true);
      }),
    );
  }

  private proceedWithoutUnsavedChanges$(): Observable<boolean> {
    return this.productPrereqForm.hasUnsavedChanges$.pipe(
      take(1),
      switchMap((hasUnsavedChanges) => {
        if (!hasUnsavedChanges) {
          return of(true);
        }

        return this.confirmationModal.openModal({
          title: 'LIB_ORDERS.SALES_ORDERS.DISCARD_UNSAVED_CHANGES_DIALOG.TITLE',
          message: 'LIB_ORDERS.SALES_ORDERS.DISCARD_UNSAVED_CHANGES_DIALOG.MESSAGE',
          confirmButtonText: 'LIB_ORDERS.SALES_ORDERS.DISCARD_UNSAVED_CHANGES_DIALOG.PROCEED_WITHOUT_CHANGES',
          cancelButtonText: 'LIB_ORDERS.SALES_ORDERS.DISCARD_UNSAVED_CHANGES_DIALOG.REVIEW_CHANGES',
          type: 'confirm',
        });
      }),
    );
  }

  /**
   * Refreshes order till status is fulfilled, activated with errors or maximum calls made
   */
  private refreshOrderWhileProcessing(): void {
    const pollingInterval = 10000;
    const maxRefreshCalls = 60;
    const getSalesOrderTime$ = interval(pollingInterval).pipe(take(maxRefreshCalls));
    const newOrder$ = combineLatest([getSalesOrderTime$, this.order$]).pipe(
      concatMap(([, order]) => this.salesOrdersService.get(order.orderId, order.businessId)),
      skipWhile((order: Order) => order.status === Status.PROCESSING),
      first(),
    );

    this.subscriptions.push(
      newOrder$.subscribe((newOrder) => {
        if (newOrder.status === Status.FULFILLED) {
          this.snackbarService.openSuccessSnack('LIB_ORDERS.SALES_ORDERS.SUCCESS.ORDER_ACTIVATED');
        } else {
          this.snackbarService.openErrorSnack('LIB_ORDERS.SALES_ORDERS.ERRORS.ACTIVATED_WITH_ERRORS');
        }
        this.refreshOrder();
      }),
    );
  }

  private businessName(): string {
    return this.business?.napData?.companyName ?? this.translateService.instant('LIB_ORDERS.SALES_ORDERS.BUSINESS');
  }

  protected readonly AppType = AppType;
  protected readonly ActivationStatus = ActivationStatus;

  public selectOrderCurrency(newCurrency: string) {
    this.currencyOverride$$.next(newCurrency);
    const lineItems = this.lineItemsComponent.getCurrentLineItems().map((lineItem) => {
      lineItem.currencyCode = newCurrency;
      return lineItem;
    });
    this.saveUpdatedLineItems(lineItems);
  }

  public saveUpdatedWholesaleSummary(wholesaleSummary: WholesaleSummary) {
    this.saveUpdatedWholesaleSummary$$.next(wholesaleSummary);
  }

  public refreshOrder() {
    this.refreshOrderDetails.emit(true);
  }

  public domainSelected(domain: string) {
    Object.keys(this.orderFormGroup.controls).forEach((key) => {
      const subFormGroup = this.orderFormGroup.get(key) as UntypedFormGroup;
      if (subFormGroup) {
        Object.keys(subFormGroup.controls).forEach((ctrlKey) => {
          // Only match controls with the format "id#domain"
          if (DynamicOrderFormAppIds.some((id) => `${id}#domain` === ctrlKey)) {
            const domainControl = subFormGroup.get(ctrlKey);
            if (domainControl) {
              domainControl.setValue(domain);
              domainControl.markAsDirty();
              domainControl.markAsTouched();
              this.salesOrderForm.updateDomainNoValidation(domain); // Update the answers in the sales order form
            }
          }
        });
      }
    });
  }
}

export function applyVariablePriceChange(
  event: { appId: string; customPrice: number; packageId?: string },
  lineItems: LineItem[],
): void {
  const lineItem = lineItems.find((item) => {
    if (!event.packageId) {
      return item.appKey?.appId === event.appId;
    }
    return item.packageId === event.packageId;
  });
  if (!lineItem) {
    return;
  }

  if (!event.packageId) {
    lineItem.cost = new Cost({ ...lineItem.cost, customPrice: event.customPrice });
  } else {
    if (!lineItem.customPrices?.length) {
      lineItem.customPrices = [];
    }
    const customPrice = lineItem.customPrices?.find((cp) => {
      return cp.productId === event.appId;
    });
    if (customPrice) {
      customPrice.customPrice = event.customPrice;
    } else {
      lineItem.customPrices.push(
        new CustomPriceMapping({
          productId: event.appId,
          customPrice: event.customPrice,
        }),
      );
    }
  }
}
