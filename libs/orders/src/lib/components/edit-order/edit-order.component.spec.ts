import { CommonModule } from '@angular/common';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { ComponentFixture } from '@angular/core/testing';
import { provideRouter } from '@angular/router';
import { AccountGroup, AccountGroupExternalIdentifiers } from '@galaxy/account-group';
import { FeatureFlagService, PartnerServiceInterfaceToken } from '@galaxy/partner';
import { byTestId, createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { ProductActivationPrereqFormComponent } from '@vendasta/businesses';
import { ConfigInterface, LineItem, Order, SalesOrdersService, Status } from '@vendasta/sales-orders';
import { MARKET_ID_TOKEN, PARTNER_ID_TOKEN } from '@vendasta/sales-ui';
import { MockComponents } from 'ng-mocks';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { lastValueFrom, of } from 'rxjs';
import EN_TRANSLATIONS from '../../assets/en_devel.json';
import { Features } from '../../core/features';
import { OrderFeature, OrderPermissionsService } from '../../core/permissions';
import { OrderAction } from '../../core/permissions/permissions';
import { OrderPermissions, PAGE_ORDER_CONFIG_TOKEN } from '../../core/tokens';
import { OrdersModule } from '../../orders.module';
import { MARKET_ID, PARTNER_ID } from '../../shared/constants';
import { OrderFormComponent } from '../order-form/order-form.component';
import { TagsComponent } from '../tags/tags.component';
import { applyVariablePriceChange, EditOrderComponent } from './edit-order.component';
import { ProjectTrackerServiceInterfaceToken } from '@vendasta/project-tracker';
import { OrderStoreService } from '../../core/orders.service';
import { OrderLineItemValidationService } from '../order-line-item-validation/order-line-item-validation.service';
import { OrdersEditTabService, OVERVIEW_TAB_INDEX } from './edit-order-tab.service';
import { DEFAULT_LANGUAGE, TranslateStore, USE_DEFAULT_LANG, USE_EXTEND, USE_STORE } from '@ngx-translate/core';
import { LEXICON_DISABLE_OTW, LEXICON_FILE_FORMAT } from '@galaxy/lexicon';

describe('EditOrderComponent', () => {
  let spectator: Spectator<EditOrderComponent>;
  let fixture: ComponentFixture<EditOrderComponent>;
  let component: EditOrderComponent;
  let tagsComponent: TagsComponent | null;

  const partnerId = 'ABC';
  const marketId = 'default';

  const createComponent = createComponentFactory({
    component: EditOrderComponent,
    imports: [
      CommonModule,
      TranslateTestingModule.withTranslations({
        en: EN_TRANSLATIONS,
      }),
      OrdersModule,
    ],
    providers: [
      provideHttpClient(),
      provideHttpClientTesting(),
      provideRouter([]),
      {
        provide: SalesOrdersService,
        useValue: {
          getConfig: jest.fn().mockReturnValue(
            of({
              partnerId: partnerId,
              marketId: marketId,
              salespersonOptions: [],
            } as ConfigInterface),
          ),
          listTags: jest.fn().mockReturnValue(of([])),
          previewOrderActivations: jest.fn().mockReturnValue(of([])),
          validateLineItems: jest.fn().mockReturnValue(of([])),
        },
      },
      {
        provide: ProjectTrackerServiceInterfaceToken,
        useValue: {
          getConfig: jest.fn().mockReturnValue(of(null)),
        },
      },
      { provide: PARTNER_ID, useValue: partnerId },
      { provide: MARKET_ID, useValue: marketId },
      { provide: PARTNER_ID_TOKEN, useValue: of(partnerId) },
      { provide: MARKET_ID_TOKEN, useValue: of(marketId) },
      {
        provide: PartnerServiceInterfaceToken,
        useValue: {
          getPartnerId: () => of(partnerId),
        },
      },
    ],
    mocks: [OrderPermissionsService],
    shallow: true,
    declarations: [MockComponents(OrderFormComponent, ProductActivationPrereqFormComponent, TagsComponent)],
  });

  type LoadComponentArgs = {
    salesOrderFormValid?: boolean;
    productPrereqFormValid?: boolean;
    unifiedOrdersFeatureFlag?: boolean;
    orderPermissions?: OrderPermissions;
    canEditOrderForms?: boolean;
    canEditTags?: boolean;
    canViewTags?: boolean;
    canSubmitWithoutRequiredFields?: boolean;
    canReviewSubmittedOrder?: boolean;
    canActivateOrder?: boolean;
    canScheduleActivation?: boolean;
    hideTagsSection?: boolean;
  };
  const loadComponent = async ({
    salesOrderFormValid = true,
    productPrereqFormValid = true,
    unifiedOrdersFeatureFlag = true,
    orderPermissions = { accessManageOrders: true, accessMarketplace: true } as OrderPermissions,
    canEditOrderForms = true,
    canEditTags = false,
    canViewTags = true,
    canSubmitWithoutRequiredFields = false,
    canReviewSubmittedOrder = false,
    canActivateOrder = false,
    canScheduleActivation = false,
    hideTagsSection = false,
  }: LoadComponentArgs = {}) => {
    spectator = createComponent({
      props: {
        business: new AccountGroup({
          accountGroupId: 'AG-123',
          externalIdentifiers: new AccountGroupExternalIdentifiers({
            partnerId: partnerId,
          }),
        }),
        order: new Order({
          orderId: 'ORD-123',
        }),
        hideTagsSection,
      },
      providers: [
        {
          provide: FeatureFlagService,
          useValue: {
            batchGetStatus: jest.fn().mockReturnValue(
              of({
                [Features.PC_UNIFIED_ORDERS_PAGE]: unifiedOrdersFeatureFlag,
              }),
            ),
          },
        },
        {
          provide: PAGE_ORDER_CONFIG_TOKEN,
          useValue: {
            orderPermissions$: of(orderPermissions),
          },
        },
        {
          provide: OrderStoreService,
          useValue: {
            order$: of(
              new Order({
                orderId: 'ORD-123',
                status: Status.DRAFTED,
              }),
            ),
            business$: of({ externalIdentifiers: { salesPersonId: 'U-789' } }),
          },
        },
        {
          provide: OrderPermissionsService,
          useValue: {
            CanView: jest.fn().mockImplementation((feature: OrderFeature) => {
              if (feature === OrderFeature.Tags) {
                return of(canViewTags);
              }
              return of(false);
            }),
            CanEdit: jest.fn().mockImplementation((feature: OrderFeature) => {
              if (feature === OrderFeature.OrderForms) {
                return of(canEditOrderForms);
              }
              if (feature === OrderFeature.Tags) {
                return of(canEditTags);
              }
              return of(false);
            }),
            CanDoAction: jest.fn().mockImplementation((action: OrderAction) => {
              if (action === OrderAction.SubmitWithoutRequiredFields) {
                return of(canSubmitWithoutRequiredFields);
              }
              if (action === OrderAction.ReviewSubmittedOrder) {
                return of(canReviewSubmittedOrder);
              }
              if (action === OrderAction.ActivateOrder) {
                return of(canActivateOrder);
              }
              if (action === OrderAction.ScheduleActivation) {
                return of(canScheduleActivation);
              }
              return of(false);
            }),
          },
        },
        {
          provide: OrderLineItemValidationService,
          useValue: {
            invalidLineItemErrors$: of({ errorCodes: [] }),
            areOrderBillingTermsValid$: of(true),
          },
        },
        {
          provide: OrdersEditTabService,
          useValue: {
            selectedTabIndex$: of(OVERVIEW_TAB_INDEX),
          },
        },
        { provide: LEXICON_DISABLE_OTW, useValue: {} },
        { provide: LEXICON_FILE_FORMAT, useValue: {} },
        { provide: USE_DEFAULT_LANG, useValue: true },
        { provide: USE_STORE, useValue: true },
        { provide: USE_EXTEND, useValue: true },
        { provide: DEFAULT_LANGUAGE, useValue: 'en' },
        { provide: TranslateStore, useValue: TranslateStore },
      ],
    });
    fixture = spectator.fixture;
    component = fixture.componentInstance;

    // child components
    component.salesOrderForm = mockOrderForm(component.salesOrderForm, salesOrderFormValid);
    component.productPrereqForm = mockPrereqForm(component.productPrereqForm, productPrereqFormValid);
    tagsComponent = spectator.query(TagsComponent);

    await fixture.whenStable();
    spectator.detectChanges();
  };

  const mockOrderForm = (form: OrderFormComponent, isValid: boolean) => {
    form.isValid$ = of(isValid);
    form.validateForm = jest.fn().mockReturnValue(isValid);
    form.orderData$ = of(null);
    return form;
  };

  const mockPrereqForm = (form: ProductActivationPrereqFormComponent, isValid: boolean) => {
    form.isValid$ = of(isValid);
    form.hasRestrictions$ = of(true);
    return form;
  };

  it('should create', async () => {
    await loadComponent();
    expect(spectator.component).toBeTruthy();
  });

  describe('validateForm', () => {
    it('should return true when both forms are valid', async () => {
      await loadComponent({ salesOrderFormValid: true, productPrereqFormValid: true });
      const isValid = await lastValueFrom(component.validateForm());
      expect(isValid).toBe(true);
    });

    it('should return false when the order form is invalid', async () => {
      await loadComponent({ salesOrderFormValid: false, productPrereqFormValid: true });
      const isValid = await lastValueFrom(component.validateForm());
      expect(isValid).toBe(false);
    });

    it('should return false when the product form is invalid', async () => {
      await loadComponent({ salesOrderFormValid: true, productPrereqFormValid: false });
      const isValid = await lastValueFrom(component.validateForm());
      expect(isValid).toBe(false);
    });

    it('should return false when both forms are invalid', async () => {
      await loadComponent({ salesOrderFormValid: true, productPrereqFormValid: false });
      const isValid = await lastValueFrom(component.validateForm());
      expect(isValid).toBe(false);
    });

    it('should return true when the order form is invalid but the user can submit without required fields', async () => {
      await loadComponent({ salesOrderFormValid: false, canSubmitWithoutRequiredFields: true });
      const isValid = await lastValueFrom(component.validateForm());
      expect(isValid).toBe(true);
    });
  });

  describe('tabs', () => {
    it('should not show tabs when unified orders feature flag is false', async () => {
      await loadComponent({ unifiedOrdersFeatureFlag: false });
      const tabs = spectator.query(byTestId('order-tabs'));
      expect(tabs).toBeNull();
    });

    it('should show tabs when unified orders feature flag is true', async () => {
      await loadComponent({ unifiedOrdersFeatureFlag: true });
      const tabs = spectator.query(byTestId('order-tabs'));
      expect(tabs).toBeVisible();
      const tabIndex = await lastValueFrom(component.selectedTabIndex$);
      expect(tabIndex).toEqual(OVERVIEW_TAB_INDEX);
    });

    it('should update selectedTabIndex tab when openOrderFormTab is called', async () => {
      await loadComponent({ unifiedOrdersFeatureFlag: true });
      component.openOrderFormTab();
      const tabIndex = await lastValueFrom(component.selectedTabIndex$);
      expect(tabIndex).toEqual(OVERVIEW_TAB_INDEX);
    });
  });

  describe('order forms', () => {
    const assertAlertVisible = () => {
      const alert = spectator.query(byTestId('invalid-forms-alert'));
      expect(alert).toBeVisible();
    };

    const assertAlertHidden = () => {
      const alert = spectator.query(byTestId('invalid-forms-alert'));
      expect(alert).toBeNull();
    };

    it('should load ViewChild OrderFormComponent', async () => {
      await loadComponent();
      expect(component.salesOrderForm).toBeInstanceOf(OrderFormComponent);
    });

    it('should load WorkOrders', async () => {
      await loadComponent();
      component.workOrders$.subscribe((workOrders) => {
        expect(workOrders).toBeTruthy();
      });
    });

    it('should load ViewChild ProductActivationPrereqFormComponent', async () => {
      await loadComponent();
      expect(component.productPrereqForm).toBeInstanceOf(ProductActivationPrereqFormComponent);
    });

    it('should not display alert when all forms are valid', async () => {
      await loadComponent({ salesOrderFormValid: true, productPrereqFormValid: true });
      assertAlertHidden();
    });

    it('should display alert when OrderFormComponent is invalid', async () => {
      await loadComponent({ salesOrderFormValid: false, productPrereqFormValid: true });
      assertAlertVisible();
    });

    it('should not display alert when canSubmitWithoutRequiredFields is false', async () => {
      await loadComponent({ salesOrderFormValid: false, canSubmitWithoutRequiredFields: true });
      assertAlertHidden();
    });

    it('should not display work orders alert when Unified Orders Feature Flag is true', async () => {
      const workOrdersAlert = spectator.query(byTestId('work-orders-alert'));
      await loadComponent({ unifiedOrdersFeatureFlag: true });
      expect(workOrdersAlert).toBeNull();
    });

    it('should display alert when ProductActivationPrereqFormComponent is invalid', async () => {
      await loadComponent({ salesOrderFormValid: true, productPrereqFormValid: false });
      assertAlertVisible();
    });

    it('should display alert when both forms are invalid', async () => {
      await loadComponent({ salesOrderFormValid: false, productPrereqFormValid: false });
      assertAlertVisible();
    });

    it('should not display alert when both forms are valid but unified orders feature flag is false', async () => {
      await loadComponent({ salesOrderFormValid: true, productPrereqFormValid: true, unifiedOrdersFeatureFlag: false });
      assertAlertHidden();
    });

    it('should be editable when OrderPermissionService CanEdit returns true', async () => {
      await loadComponent({
        salesOrderFormValid: true,
        productPrereqFormValid: true,
        unifiedOrdersFeatureFlag: true,
        canEditOrderForms: true,
      });
      expect(component.salesOrderForm.orderFormOptions.readOnly).toBe(false);
      expect(component.productPrereqForm.orderFormOptions.readOnly).toBe(false);
    });

    it('should not be editable when OrderPermissionService CanEdit returns false', async () => {
      await loadComponent({
        salesOrderFormValid: true,
        productPrereqFormValid: true,
        unifiedOrdersFeatureFlag: true,
        canEditOrderForms: false,
      });
      expect(component.salesOrderForm.orderFormOptions.readOnly).toBe(true);
      expect(component.productPrereqForm.orderFormOptions.readOnly).toBe(true);
    });
  });

  describe('tags', () => {
    describe('view', () => {
      it('should be visible when OrderPermissionService CanView returns true', async () => {
        await loadComponent({
          canViewTags: true,
        });
        expect(tagsComponent).toBeVisible();
      });

      it('should not be visible when OrderPermissionService CanView returns true BUT hideTagsSection input is true', async () => {
        await loadComponent({
          canViewTags: true,
          hideTagsSection: true,
        });
        expect(tagsComponent).toBeNull();
      });

      it('should not be visible when OrderPermissionService CanView returns false', async () => {
        await loadComponent({
          canViewTags: false,
        });
        expect(tagsComponent).toBeNull();
      });
    });

    describe('edit', () => {
      it('should be editable when OrderPermissionService CanEdit returns true', async () => {
        await loadComponent({
          canEditTags: true,
        });

        expect(tagsComponent).toBeVisible();
        expect(tagsComponent.viewOnly).toBe(false);
      });

      it('should not be editable when OrderPermissionService CanEdit returns false', async () => {
        await loadComponent({
          canEditTags: false,
        });
        expect(tagsComponent).toBeVisible();
        expect(tagsComponent.viewOnly).toBe(true);
      });
    });
  });

  describe('footer', () => {
    const footer = () => spectator.query(byTestId('order-footer'));
    const approveButton = () => spectator.query(byTestId('approve-submitted-order-button'));
    const declineButton = () => spectator.query(byTestId('decline-submitted-order-button'));
    const activateOrderButton = () => spectator.query(byTestId('activate-order-button'));
    const scheduleActivationButton = () => spectator.query(byTestId('schedule-activation-button'));

    it('should show the approve button when CanReviewSubmittedOrder is true', async () => {
      await loadComponent({
        canReviewSubmittedOrder: true,
      });

      expect(footer()).toBeVisible();
      expect(approveButton()).toBeVisible();
    });

    it('should hide the approve button when CanReviewSubmittedOrder is false', async () => {
      await loadComponent({
        canReviewSubmittedOrder: false,
      });

      expect(approveButton()).not.toBeVisible();
    });

    it('should show the decline button when CanReviewSubmittedOrder is true', async () => {
      await loadComponent({
        canReviewSubmittedOrder: true,
      });

      expect(footer()).toBeVisible();
      expect(declineButton()).toBeVisible();
    });

    it('should hide the decline button when CanReviewSubmittedOrder is false', async () => {
      await loadComponent({
        canReviewSubmittedOrder: false,
      });

      expect(declineButton()).not.toBeVisible();
    });

    it('should show the activate now button when CanActivateOrder is true', async () => {
      await loadComponent({
        canActivateOrder: true,
      });

      expect(footer()).toBeVisible();
      expect(activateOrderButton()).toBeVisible();
    });

    it('should hide the activate now button when CanActivateOrder is false', async () => {
      await loadComponent({
        canActivateOrder: false,
      });

      expect(activateOrderButton()).not.toBeVisible();
    });

    it('should show the activate on contract start button when CanScheduleActivation is true', async () => {
      await loadComponent({
        canScheduleActivation: true,
      });

      expect(footer()).toBeVisible();
      expect(scheduleActivationButton()).toBeVisible();
    });

    it('should hide the activate on contract start button when CanScheduleActivation is false', async () => {
      await loadComponent({
        canScheduleActivation: false,
      });

      expect(scheduleActivationButton()).not.toBeVisible();
    });
  });
});

describe('applyVariablePriceChange', () => {
  const defaultLineItems: LineItem[] = buildDefaultLineItems();

  it('should update the custom price for a product line item with custom pricing', () => {
    const lineItems = buildDefaultLineItems();
    applyVariablePriceChange({ appId: 'MP-123', customPrice: 200 }, lineItems);
    expect(lineItems).toEqual([{ ...defaultLineItems[0], cost: { customPrice: 200 } }, ...defaultLineItems.slice(1)]);
  });
  it('should update the custom price for a product line item without custom pricing', () => {
    const lineItems = buildDefaultLineItems();
    applyVariablePriceChange({ appId: 'MP-456', customPrice: 250 }, lineItems);
    expect(lineItems).toEqual([
      defaultLineItems[0],
      { ...defaultLineItems[1], cost: { customPrice: 250 } },
      defaultLineItems[2],
    ]);
  });
  it('should update the custom price for a package line item with custom pricing', () => {
    const lineItems = buildDefaultLineItems();
    applyVariablePriceChange({ appId: 'MP-ABC', customPrice: 150, packageId: 'SOL-123' }, lineItems);
    expect(lineItems).toEqual([
      ...defaultLineItems.slice(0, 2),
      {
        ...defaultLineItems[2],
        customPrices: [{ productId: 'MP-ABC', customPrice: 150 }, defaultLineItems[2].customPrices[1]],
      },
    ]);
  });
  it('should update the custom price for a package line item without custom pricing', () => {
    const lineItems = buildDefaultLineItems();
    applyVariablePriceChange({ appId: 'MP-DEF', customPrice: 250, packageId: 'SOL-123' }, lineItems);
    expect(lineItems).toEqual([
      ...defaultLineItems.slice(0, 2),
      {
        ...defaultLineItems[2],
        customPrices: [...defaultLineItems[2].customPrices, { productId: 'MP-DEF', customPrice: 250 }],
      },
    ]);
  });
});

function buildDefaultLineItems(): LineItem[] {
  return [
    new LineItem({ appKey: { appId: 'MP-123' }, cost: { customPrice: 100 } }),
    new LineItem({ appKey: { appId: 'MP-456' } }),
    new LineItem({
      packageId: 'SOL-123',
      customPrices: [
        { productId: 'MP-ABC', customPrice: 100 },
        { productId: 'MP-GHI', customPrice: 300 },
      ],
    }),
  ];
}
