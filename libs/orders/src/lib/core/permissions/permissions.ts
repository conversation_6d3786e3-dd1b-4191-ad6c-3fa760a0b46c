import { Observable } from 'rxjs';

export type FeaturePermissions = {
  view: boolean;
  edit: boolean;
};

export enum OrderFeature {
  OrderForms = 'orderForms',
  Tags = 'tags',
  OrderContents = 'orderContents',
  Invoices = 'invoices',
  FulfillmentStatuses = 'fulfillmentStatuses',
  CreateOrderDetails = 'createOrderDetails',
  ActiveItems = 'activeItems',
  ContractStartAndDuration = 'contractStartAndDuration',
  InvalidLineItemsBanner = 'invalidLineItemsBanner',
  WholesalePriceColumn = 'wholesalePriceColumn',
}

export type OrderFeaturePermissions = {
  [key in OrderFeature]: Observable<FeaturePermissions>;
};

export enum OrderAction {
  SubmitWithoutRequiredFields = 'submitWithoutRequiredFields',
  IgnoreAllErrorsOnOrder = 'ignoreAllErrorsOnOrder',
  CreateInvoiceFromOrder = 'createInvoiceFromOrder',
  SubmitForCustomerApproval = 'submitForCustomerApproval',
  DuplicateOrder = 'duplicateOrder',
  SubscribeToUpdates = 'subscribeToUpdates',

  CancelOrder = 'cancelOrder',
  RequestToCancelOrder = 'requestToCancelOrder',
  ReviewCancellationRequest = 'reviewCancellationRequest',
  ArchiveOrder = 'archiveOrder',
  ActivateOrder = 'activateOrder',
  ScheduleActivation = 'scheduleActivation',
  ReviewSubmittedOrder = 'reviewSubmittedOrder',
  SelectOrderCurrency = 'selectOrderCurrency',
  AccessOnlySalespersonAutomations = 'accessOnlySalespersonAutomations',
  UpdateInvalidContractStartDate = 'updateInvalidContractStartDate',
  RedirectToCompanyProfileAfterSubmitting = 'redirectToCompanyProfileAfterSubmitting',
  CollectPaymentFromCustomer = 'collectPaymentFromCustomer',
  ChargeSMBOnOrderSubmission = 'chargeSMBOnOrderSubmission',
  BypassAdminWorkflowAndActivate = 'BypassAdminWorkflowAndActivate',
}

export type OrderActionPermissions = {
  [key in OrderAction]: Observable<boolean>;
};
