import { SalespersonOptionsInterface, Status, WorkflowStepOptionsInterface } from '@vendasta/sales-orders';
import { FeaturePermissions } from './permissions';

type GetOrderFormPermissionsParams = {
  orderStatus: Status;
  workflowStepOptions: WorkflowStepOptionsInterface;
  canManageOrders: boolean;
};

export function GetOrderFormPermissions({
  orderStatus,
  workflowStepOptions,
  canManageOrders,
}: GetOrderFormPermissionsParams): FeaturePermissions {
  const canEditUntilActivation = workflowStepOptions.allowOrderFormEditing || canManageOrders;

  const editableStatuses = [Status.DRAFTED];
  if (canEditUntilActivation) {
    editableStatuses.push(Status.SUBMITTED, Status.RESUBMITTED, Status.APPROVED);
  }
  const canEdit = editableStatuses.includes(orderStatus);

  return { view: true, edit: canEdit };
}

type GetTagPermissionsParams = {
  salespersonOptions: SalespersonOptionsInterface;
  canManageOrders: boolean;
};

export function GetTagPermissions({
  salespersonOptions,
  canManageOrders,
}: GetTagPermissionsParams): FeaturePermissions {
  const canEdit = canManageOrders || !salespersonOptions.disableTagging;

  return { view: true, edit: canEdit };
}

type GetEditOrderContentsPermissionsParams = {
  orderStatus: Status;
  canManageOrders: boolean;
};

export function GetEditOrderContentsPermissions({
  orderStatus,
  canManageOrders,
}: GetEditOrderContentsPermissionsParams): FeaturePermissions {
  const adminEditableStatuses = [Status.SUBMITTED, Status.RESUBMITTED, Status.APPROVED, Status.SCHEDULED_ACTIVATION];
  const adminCanEdit = canManageOrders && adminEditableStatuses.includes(orderStatus);

  const canEdit = adminCanEdit || orderStatus === Status.DRAFTED;

  return { view: true, edit: canEdit };
}

type GetInvoicePermissionsParams = {
  canManageOrders: boolean;
  autoGenerateRetailSubscriptions: boolean;
};

export function GetInvoicePermissions({
  canManageOrders,
  autoGenerateRetailSubscriptions,
}: GetInvoicePermissionsParams): FeaturePermissions {
  const canViewAndEdit = canManageOrders && autoGenerateRetailSubscriptions;
  return { view: canViewAndEdit, edit: canViewAndEdit };
}

export function GetFulfillmentStatusesPermissions(orderStatus: Status): FeaturePermissions {
  const postSubmissionStatuses: Status[] = [
    Status.FULFILLED,
    Status.PROCESSING,
    Status.ACTIVATION_ERRORS,
    Status.ARCHIVED,
    Status.CANCELLATION_REQUESTED,
    Status.CANCELLED,
    Status.AWAITING_PAYMENT,
    Status.PROCESSING_PAYMENT,
  ];
  const canViewAndEdit = postSubmissionStatuses.includes(orderStatus);
  return { view: canViewAndEdit, edit: canViewAndEdit };
}

export function GetCreateOrderDetailsPermissions(orderStatus: Status, canManageOrders: boolean): FeaturePermissions {
  const isDraft = orderStatus === Status.DRAFTED;
  const canEditOrderContents = GetEditOrderContentsPermissions({
    orderStatus: orderStatus,
    canManageOrders: canManageOrders,
  }).edit;
  return { view: isDraft, edit: isDraft && canEditOrderContents };
}

export function GetActiveItemsPermissions(orderStatus: Status): FeaturePermissions {
  const canViewAndEdit = orderStatus === Status.DRAFTED;
  return { view: canViewAndEdit, edit: canViewAndEdit };
}

type GetEditOrderContractStartAndDurationPermissionsParams = {
  orderStatus: Status;
  canManageOrders: boolean;
};

export function GetEditOrderContractStartAndDurationPermissions({
  orderStatus,
  canManageOrders,
}: GetEditOrderContractStartAndDurationPermissionsParams): FeaturePermissions {
  const adminEditableStatuses = [Status.SUBMITTED, Status.RESUBMITTED, Status.APPROVED];
  const adminCanEdit = canManageOrders && adminEditableStatuses.includes(orderStatus);

  const canEdit = adminCanEdit || orderStatus === Status.DRAFTED;

  return { view: true, edit: canEdit };
}

export function GetInvalidLineItemsBannerPermissions(orderStatus: Status): FeaturePermissions {
  const showBannerStatuses: Status[] = [
    Status.DRAFTED,
    Status.SUBMITTED,
    Status.RESUBMITTED,
    Status.APPROVED,
    Status.SCHEDULED_ACTIVATION,
  ];
  const canViewAndEdit = showBannerStatuses.includes(orderStatus);
  return { view: canViewAndEdit, edit: canViewAndEdit };
}

export function GetWholesaleColumnPermissions(canManageMarketplace: boolean): FeaturePermissions {
  return { view: canManageMarketplace, edit: canManageMarketplace };
}
